{"__meta": {"id": "X3ecd5c226249810ac2c9bbcc28dcefa7", "datetime": "2025-07-29 06:38:32", "utime": **********.510129, "method": "GET", "uri": "/api/leads/21", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753771111.54892, "end": **********.510153, "duration": 0.9612331390380859, "duration_str": "961ms", "measures": [{"label": "Booting", "start": 1753771111.54892, "relative_start": 0, "end": **********.393999, "relative_end": **********.393999, "duration": 0.8450791835784912, "duration_str": "845ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.394011, "relative_start": 0.8450911045074463, "end": **********.510155, "relative_end": 1.9073486328125e-06, "duration": 0.11614394187927246, "duration_str": "116ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46081216, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/leads/{id}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ContactController@getLeadForPreview", "namespace": null, "prefix": "", "where": [], "as": "api.leads.show", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=531\" onclick=\"\">app/Http/Controllers/ContactController.php:531-567</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.01363, "accumulated_duration_str": "13.63ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4477139, "duration": 0.00951, "duration_str": "9.51ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 0, "width_percent": 69.773}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.473016, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 69.773, "width_percent": 10.345}, {"sql": "select * from `leads` where `id` = '21' and `created_by` = 84 limit 1", "type": "query", "params": [], "bindings": ["21", "84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 537}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4789019, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:537", "source": "app/Http/Controllers/ContactController.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=537", "ajax": false, "filename": "ContactController.php", "line": "537"}, "connection": "omx_sass_systam_db", "start_percent": 80.117, "width_percent": 6.603}, {"sql": "select * from `lead_stages` where `lead_stages`.`id` in (118)", "type": "query", "params": [], "bindings": ["118"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 537}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.487849, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "ContactController.php:537", "source": "app/Http/Controllers/ContactController.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=537", "ajax": false, "filename": "ContactController.php", "line": "537"}, "connection": "omx_sass_systam_db", "start_percent": 86.72, "width_percent": 8.291}, {"sql": "select * from `pipelines` where `pipelines`.`id` in (30)", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 537}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.49218, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:537", "source": "app/Http/Controllers/ContactController.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=537", "ajax": false, "filename": "ContactController.php", "line": "537"}, "connection": "omx_sass_systam_db", "start_percent": 95.011, "width_percent": 4.989}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}, "App\\Models\\LeadStage": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLeadStage.php&line=1", "ajax": false, "filename": "LeadStage.php", "line": "?"}}, "App\\Models\\Pipeline": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPipeline.php&line=1", "ajax": false, "filename": "Pipeline.php", "line": "?"}}}, "count": 4, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/contacts\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/api/leads/21", "status_code": "<pre class=sf-dump id=sf-dump-1644861611 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1644861611\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-916840811 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-916840811\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1423025968 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1423025968\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-198232089 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6Ii84NWljVGFHandPK25ZVHdoanNLSkE9PSIsInZhbHVlIjoiYVlZb2dZWW9qREp6NEJKMVFycEtiTE1LT1VTOSt3Vlo5d254U0pKVmVUeFhpNmI2dm9tb0o5YTJDMVdjL3RIRGcyVEZtUk9LV0ZiK3NKSkpBTzAyZzNIdU4zVk9sRldYRXNaKzVqSUR6dmFaOFk3T2tHYTJkMHFkOG5UTSt3d0tOSnFKbEQwR0tuakpWTlU3amhtUDFuVVVGZTZ5UVIwNDNZVzhhQVVjSGpNbVpvZzVqOHB0aUtuMjhBSVNodkwyVU1qMjliSEd0L2ozU0d4MWtIc1d3cVByYjV1Q001R0YzSUtwZkdjM2MzWlh3d0VlSWVTUTNicDFVK3lSeDYxOENZWWRSSXhxSTVQT2JmZDhMeURHTEFYYkJGNjk4Y3BhU2J2YUpodTE5RlZHNGNlQW1ueU1DMHBKdlRLL1BkaWlCL2NNSzRzQkhQYWlreGI2ejVXeUdwV3BOOUkzOHc4OVE0aGNQeDF0UzJxZGZUR0hGdDFjSmovamF3Q1F3c1hqL21VM2hMbHUwb1B0clIvbzNiM0JHNzd5b1dLS3ZVUTRrbWZ6UnZSRCtBSFR3NEk3dzh4eGF5MzZjQytlc0p0WWNWbVNMems1WlFxWXFKdHpGMXhvOGRiMXJqV29KcS85UjVRZnZDRHJuYTB6cUt6bGo3ZXcrZmlTMHMwemJUaXAiLCJtYWMiOiI0NjZlNzcwNGE2Njk4NTAxY2FkNmQyZDI5MzE2ODY4ZDAwYjMyZTc2M2EwYTkwM2E5NWUwODIyM2EwZDJlZjA1IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlpSOHpIMGhDTzZiYzFHUVJlR1JUN0E9PSIsInZhbHVlIjoiNTRzT2pMOERqNVduajFObzRsbTNkSTV6NllxQldDZDBvVmZvZ3hoOEhLak5ROEJLOS9DNklRdzRKUWcxcllwbitZRk5JZWtOSXVqSGRuVjZDT0l3aWpva283eEc3SGF3RXExS2R0MkQyMUxUOGdyNGR3NjV2N2dvWFd1Z1BYRWxNdEpiOERzOFN2YWc1eHBRdHJxQ1IzSnZhSk1uN3Z2WmxKcUtEUUR4Nk92SjJBWjdOYkc2RExMQk1zZWI2RTNZdFloSjNKc2JiN0JuYlFkTjhUeVpIeHVqc2NRL3ZxdzF0TnNNQmlBSHluM3hURWUzS3pEcjMrQjlkY1ZETExwWUZPeXBSQXlMclR4dGdYYTI1ZEZlRDB1R3BEVGI0WXRVaVNCU3FyRnJVRlFyU1RJN1dENU5yWXFhOEFjTTNSQW1kQ1ZnZmVTT3RScWs2WHZHMExBT0xITmc0SEdqcDE3S2JqZU1CU0ZUS09LNWxodC85OE55VzlSYlprbEpBMWpHN1dmSGRwTFpFT0RpWEd5RU5WRXdYcnJlVGlGVjkwSE1sZ2R1K0FoRWRFUHYvMzd6K0xwZXpBUmhvNVFlWGx0UE9pa1FkcU5UeE9Qa0N4bTh6MVc0Q2l0QndrSERacjZnM3RoOGtjY0dCOThEMDJTOTZMK1VQUkMrM2FGckRnVmciLCJtYWMiOiI0MTZkYWQ5ZDQxMDFhNDcwMTM4MzcwMTI2MzAzNWFhYjc1ZTE4NDkyYjliZGNlMmUwMmEwMDljYzQ2ODRmYTRmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-198232089\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-294933958 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1XDrxTWmYv3gorVrXvmSodhuVHNiE4Wue14QMI4v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-294933958\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1458644753 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 06:38:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkRYQ2RoWTYrK1ZMQ21uRFArUG9qenc9PSIsInZhbHVlIjoiL1JxRGh3MEJkeDZmMnVjaEE2eUthTUxZOWhtU0k1WG5EMG5uMHRFeHB3VG1SUjBNQzJwSWFMY2VaZTk3YjYwQmRGdmZ2STZVM0VxaGdmT2h1MHN0ZnBFQXhuWVlTWjlPUnJpM2dsa1luNnFzY2JabWtjSFI5cmJSNWNRMktRaGR2V2ZFSGJTeHpxbGVZRkZ3djJlMXY5MFl5SWNhVmRjU0VMZUdmOS9IRlN4ZjlpaERJc1ZMczMwU211dithTEwvdGlxa01YSFRmUkJxcHdFdzUrR3MwMDd3VmlJSXlkSTU2T25YbzNESzM3bW0vRjlHc3I0SG1RQ0JBc3BHei80Vm80dGMzSXFPV3VuL1JWQ2o3N2svTE94UG1zNXowYURvWnJGVm12Q2k5VU1jd1piMm12TWVoQWo5cUlVakxXM1lBbHJWZTg1SjNIZXQ3MFlIa296VXVDUjBEbkRQdEQxL080NER5c2hYRjJuRVpOUTMxRzhoMFU3RGF2ZE9mMVhBUmU4SnFKeHJkUWhTeUdEUmJyYUs5Si9mWHVvS3hFTTZza290b1VKZjBDdkRuMUlheGtpY0JjVVBGNWFrdTlhaXVwcFZJNzNKZmd0MkRaSEp0bUpyN2s5Nld4ZS9VZHNLUUdSK09jRFhzak5DNTNGRW9Rc1EzdVFVbnRKSXJQVkQiLCJtYWMiOiI1N2NjN2U5NDU3OTRkNWE4YTZmNDdiM2UxMmUzMDIzMzg3ODVkZDcxNzkzOGY5NGYzZjM0Mjc3ZTExMDg2NjA2IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:38:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkVmbmlTbldXWGxsSlh6WkhLaTR1cHc9PSIsInZhbHVlIjoiMWc2MlV5WUFZaVNqc2RxNmQwdmptSVl6MnlJOEN3T1VJeHdOamRaQmx0TVFGRlUySWV1cWJJRDhYZnpQVEltMnpxZXROeVY3ZTR1dlltVG1aNzlQN0RjWU54YmNJSmV5WnZRZmF1WHh2amlyUEhjOHVyUkw4czN2OTFEVXVDbE9aZVZXQnlvaHdaOGlTZG1ybCtpQ1E5YmpzbXZSZ3gxaXNMYzlhc1VPNFpnNkR2QmEyZEhDL0Z2bjZoeWpQV1gxVmllTXhSaExHMDFBUmJ1K3BCV3hFK1dSVnJQOUIyYktOTUZ4dXpTdENjNW5KMCtGcWY0N1NDSE9GdGVIbTVWT3pGMmdnL01xaWVSQkVMdW5hN3EyTlhpZVh2WGxBcG0ySUQyMFlhTmtqM1EzaGM0MWgzWnUwb2wxZktKcno2TVE2RlcwS0kxYjRFVTFQSWRCVzJ4VFVscCtndlNiY2RDVTFWd0EvYU5YbU04MkJJK2c3UStLY0hWV1Z3TG9VWjU1dWQ3QVRKckJUUlRCTDUvbzhYMllMaVNwM3lsOGIzNWg2VXFlUlB1MmtjUnBKczhINjEyUTErbWcxVUtUMUtsK0NUVWlpOE00aXl3VlMvTnJ0Q3R5dTNxQmx6djNmdWh2SmxpR1FEaXJhLzRCMTJWU1h0bitvZWlCVGNOeExOZHMiLCJtYWMiOiJmMTlmZTczNzI3YTZjMjgzZmEyODlmODY5ZTU2YWZlYmFkOTFhODc1YjEyNzg4NWRkNzc4ZWM5ZjQwMjQ1YzJjIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:38:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkRYQ2RoWTYrK1ZMQ21uRFArUG9qenc9PSIsInZhbHVlIjoiL1JxRGh3MEJkeDZmMnVjaEE2eUthTUxZOWhtU0k1WG5EMG5uMHRFeHB3VG1SUjBNQzJwSWFMY2VaZTk3YjYwQmRGdmZ2STZVM0VxaGdmT2h1MHN0ZnBFQXhuWVlTWjlPUnJpM2dsa1luNnFzY2JabWtjSFI5cmJSNWNRMktRaGR2V2ZFSGJTeHpxbGVZRkZ3djJlMXY5MFl5SWNhVmRjU0VMZUdmOS9IRlN4ZjlpaERJc1ZMczMwU211dithTEwvdGlxa01YSFRmUkJxcHdFdzUrR3MwMDd3VmlJSXlkSTU2T25YbzNESzM3bW0vRjlHc3I0SG1RQ0JBc3BHei80Vm80dGMzSXFPV3VuL1JWQ2o3N2svTE94UG1zNXowYURvWnJGVm12Q2k5VU1jd1piMm12TWVoQWo5cUlVakxXM1lBbHJWZTg1SjNIZXQ3MFlIa296VXVDUjBEbkRQdEQxL080NER5c2hYRjJuRVpOUTMxRzhoMFU3RGF2ZE9mMVhBUmU4SnFKeHJkUWhTeUdEUmJyYUs5Si9mWHVvS3hFTTZza290b1VKZjBDdkRuMUlheGtpY0JjVVBGNWFrdTlhaXVwcFZJNzNKZmd0MkRaSEp0bUpyN2s5Nld4ZS9VZHNLUUdSK09jRFhzak5DNTNGRW9Rc1EzdVFVbnRKSXJQVkQiLCJtYWMiOiI1N2NjN2U5NDU3OTRkNWE4YTZmNDdiM2UxMmUzMDIzMzg3ODVkZDcxNzkzOGY5NGYzZjM0Mjc3ZTExMDg2NjA2IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:38:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkVmbmlTbldXWGxsSlh6WkhLaTR1cHc9PSIsInZhbHVlIjoiMWc2MlV5WUFZaVNqc2RxNmQwdmptSVl6MnlJOEN3T1VJeHdOamRaQmx0TVFGRlUySWV1cWJJRDhYZnpQVEltMnpxZXROeVY3ZTR1dlltVG1aNzlQN0RjWU54YmNJSmV5WnZRZmF1WHh2amlyUEhjOHVyUkw4czN2OTFEVXVDbE9aZVZXQnlvaHdaOGlTZG1ybCtpQ1E5YmpzbXZSZ3gxaXNMYzlhc1VPNFpnNkR2QmEyZEhDL0Z2bjZoeWpQV1gxVmllTXhSaExHMDFBUmJ1K3BCV3hFK1dSVnJQOUIyYktOTUZ4dXpTdENjNW5KMCtGcWY0N1NDSE9GdGVIbTVWT3pGMmdnL01xaWVSQkVMdW5hN3EyTlhpZVh2WGxBcG0ySUQyMFlhTmtqM1EzaGM0MWgzWnUwb2wxZktKcno2TVE2RlcwS0kxYjRFVTFQSWRCVzJ4VFVscCtndlNiY2RDVTFWd0EvYU5YbU04MkJJK2c3UStLY0hWV1Z3TG9VWjU1dWQ3QVRKckJUUlRCTDUvbzhYMllMaVNwM3lsOGIzNWg2VXFlUlB1MmtjUnBKczhINjEyUTErbWcxVUtUMUtsK0NUVWlpOE00aXl3VlMvTnJ0Q3R5dTNxQmx6djNmdWh2SmxpR1FEaXJhLzRCMTJWU1h0bitvZWlCVGNOeExOZHMiLCJtYWMiOiJmMTlmZTczNzI3YTZjMjgzZmEyODlmODY5ZTU2YWZlYmFkOTFhODc1YjEyNzg4NWRkNzc4ZWM5ZjQwMjQ1YzJjIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:38:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1458644753\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-377742106 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-377742106\", {\"maxDepth\":0})</script>\n"}}