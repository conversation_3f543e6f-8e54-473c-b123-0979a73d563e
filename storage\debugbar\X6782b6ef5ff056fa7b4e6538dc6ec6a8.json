{"__meta": {"id": "X6782b6ef5ff056fa7b4e6538dc6ec6a8", "datetime": "2025-07-29 06:38:00", "utime": **********.506104, "method": "GET", "uri": "/api/leads/21", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753771079.490439, "end": **********.506145, "duration": 1.0157060623168945, "duration_str": "1.02s", "measures": [{"label": "Booting", "start": 1753771079.490439, "relative_start": 0, "end": **********.331282, "relative_end": **********.331282, "duration": 0.8408429622650146, "duration_str": "841ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.331317, "relative_start": 0.8408780097961426, "end": **********.506148, "relative_end": 3.0994415283203125e-06, "duration": 0.17483115196228027, "duration_str": "175ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46094952, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/leads/{id}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ContactController@getLeadForPreview", "namespace": null, "prefix": "", "where": [], "as": "api.leads.show", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=531\" onclick=\"\">app/Http/Controllers/ContactController.php:531-567</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.01572, "accumulated_duration_str": "15.72ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.418545, "duration": 0.01174, "duration_str": "11.74ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 0, "width_percent": 74.682}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4649549, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 74.682, "width_percent": 7.252}, {"sql": "select * from `leads` where `id` = '21' and `created_by` = 84 limit 1", "type": "query", "params": [], "bindings": ["21", "84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 537}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.471298, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "ContactController.php:537", "source": "app/Http/Controllers/ContactController.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=537", "ajax": false, "filename": "ContactController.php", "line": "537"}, "connection": "omx_sass_systam_db", "start_percent": 81.934, "width_percent": 6.87}, {"sql": "select * from `lead_stages` where `lead_stages`.`id` in (118)", "type": "query", "params": [], "bindings": ["118"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 537}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.48211, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:537", "source": "app/Http/Controllers/ContactController.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=537", "ajax": false, "filename": "ContactController.php", "line": "537"}, "connection": "omx_sass_systam_db", "start_percent": 88.804, "width_percent": 6.298}, {"sql": "select * from `pipelines` where `pipelines`.`id` in (30)", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 537}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.487212, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:537", "source": "app/Http/Controllers/ContactController.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=537", "ajax": false, "filename": "ContactController.php", "line": "537"}, "connection": "omx_sass_systam_db", "start_percent": 95.102, "width_percent": 4.898}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}, "App\\Models\\LeadStage": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLeadStage.php&line=1", "ajax": false, "filename": "LeadStage.php", "line": "?"}}, "App\\Models\\Pipeline": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPipeline.php&line=1", "ajax": false, "filename": "Pipeline.php", "line": "?"}}}, "count": 4, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/contacts\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/api/leads/21", "status_code": "<pre class=sf-dump id=sf-dump-1952897724 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1952897724\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1837655864 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1837655864\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1058882637 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1058882637\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1949848179 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6Ik0zYTZ5ODNGT3gyZlg0WnEvVTZPb3c9PSIsInZhbHVlIjoiWVlRcHNjd0h0U3JDU0ZVRDN3UnVGUXdrdDE2b0pkRUppYkpBYTd5THIzVzBGeGxsaExZOXlISEgyaTFVNEpPSGtNVnlDTENtZWkvV0JtZGQ2cWxDWmJZa1NLYlF5NG43ZUJub0NpeitsY044UzV5WFNOZWpFR3k3YzlVZDR0VitzYlFQaFJkdGMvZXhVczlaOWdJSnBWUytiRmdHL1NiUGJyRXJmOUZhV2c5b3V3eWtjQld6Z3N0NW1LeU5pd3VOaDJZV3RFSGJaYkdUKzNGU0Zpay84Y29xc3pVcnJhVXFLY0NlcFZxemVaVzdacFZNYnpLMEZsZGNCTGNUN1F6TnEyWjUxNWZadmdaaDhZc1N5VnpwYzFRMkE4emUyYUhrcG8zcVdwemQ4bUZ5R0tMNy9pekRac21uMWRGMmYwK2lPa09adGxuM05YcnFabVVYbkxPQkI2RGZUUnMzem91YWt6aC95eVVxMzhwU1NIa3pNa1ZRMHlHTnZ5RWNVcm5ldW4rMGlEUTkwU0duRzIwcjA3cE42d3pzalhjekNFcTdRNjhBS05XbmhWV0ZCUU53MDd3M3paQmc3aU1ndTNTcS9KYlVoczAwUGVORFpLcHE5UzdxUjg0TGJpNXp1eFl4WWFuRGJvWmQ1aWVKYlY1ZDVuTjNONDdoNVF6VzVsalciLCJtYWMiOiI2ZmQ2NzE5OTdkMzUwZTRiNzJiYzA4N2NkYTBmODhmYzVlMWNmMTY0YWZiY2VkZTg5YmRiZjJjMmVkN2FiYmNmIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlpNaXJrQnNJdTNReER6djR2NjdaOFE9PSIsInZhbHVlIjoiT1N3c1BkRDFSTXdBTzZwOEc5VFdNV0EyaStUY0VXN0JJaWoxVG1nRGc0Syt6VGlEUFovLzR3bkt2Q2Z6Z1ZzL2drNlRPTFpKRTJ4eVp5U211VG9xVTdQRHhDanIwdkhTRit3bkRhWW1IeERZSWhjajlEdkpaNS9MY3NqTXB1OGFYNUptM0phTEdwYmdDWjVac3JKL1NZYm14QndNU3VDaVE2VWNtZjNjbjBLeFcyR21Sb0FkY2JuN1FwWHBVeHRNZG1xSDFpejZXTUh2K2h5VmJSTzUwN2ZDTEp6SWhrSWd1T0g4UmU0Z2tocEwwdnZaMGVlUXYvR2pKd29kWWZVUS9PZ0xGMnBQaXBTOFVKc1JjS1NlR3pFREk0dlBxM29GWXQ3czRCTUx4QVF6dmZYdzZxUU94eERUUEZ2L3d0d3Ayc00zQ2pzcFJSSXZhRFYwMmYySDd1Ymh2VWRZVVVTV2ZFdC9MbDR5QnV6UzhTdm1ET1A4a1ROUFhJbUhXMHFKQ0pncC9qOWFhbHY1WTd1cDJUcDZOUGZmYkdVcldqQ2RZYmZ4UWszaWgxN3JxWVdlbmdaWWdBenkwbnhKZmhvck5jWGpUR0ozcXpvTXVqd0RPNWJmcDhSMy9iUEZ3YThsMXJkbjgvVEdmYzhDVHd6VXE3bXJvRmt5MDFFZlpWUFkiLCJtYWMiOiI2MWFlNTdjMjIyODU2NGY4ZWU2M2Q2ZDljNjhhMjUxMmM3ODM3MjYzOTI5NGZlM2E0MmQwYmQxOTBhMWFjZTU5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1949848179\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1124752 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1XDrxTWmYv3gorVrXvmSodhuVHNiE4Wue14QMI4v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1124752\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-708729374 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 06:38:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkFCZ2M2L05JU0J6VmUydGdDSVlMd1E9PSIsInZhbHVlIjoid2hDa3VxMmpTNC84ek1CMU9NM1lwNnd2ZmZWU3lNZHA5anl4RVpGcHp4cEEyUWswRHRsVXVMT0Y4bng2cDNvS3ZUWFpOMEFOaWlWRG1UVmtiaEVlS1hZMWVoT1Z3ckJZcFMwY0cyK1N3b3pFTlRTUXU1YVQxckhsTzBLanFLZnhUOFJEaENCenFvQnhhdFJrLzdzdks5TDFBekNlNSszdHJJUjZ2N1lPWVRDeWhsM2Z6a1JWYUZuVXE0TUlrSXRCSmVDM040T0ZKbEtVcWUrU2dSQ0hlYmduNnFqWE1XcFdJSUs2R3l1UjhuSzhxcUdCZDliQVBiRkZ6ZkVFWElJcm5CVHozYzF5czJhSlhDZitVNmR0aEN3bklxbDgzWHlqOHB4dHhCayt0WUFnOE51elBwQStMNm96L0RwUWI2SGZxcThocThPVGdMU1pvZHdwRGUwaWpjYWVlbVhUMkF4Y0IvRUUyS0VGYmdmc0VoOHRxR0diVHJ6SDNhOUplUWN2UUYzUVRhTkMwcXdxTGtYaDBsaFd6TVVWNGNXUnJ3cFJNWnhSOVFRazdYa0xxWndJd1lFWjAvbXFSSzN6V3gyWjZtemo0VXprV3E1djhSS2FERlVleVFEZS9BOVU5Z1hKTWROK3E4R09KOXVtTlE5TjEybFNQSTVXVDI5d0NRTUkiLCJtYWMiOiIxNjdhMGIwZTZmMmM5NDUwODg1ZDYwODFiZTllODk2NDYyMWU2ZDVhMzdjODBiYWFhMTQ3ZWM1YWQwYjczYTYyIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:38:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkR3djA1bWVRQkxhazY2MWMxOG9xOEE9PSIsInZhbHVlIjoiTkhiQldYc3Y2ZWxYcTJoMkdKVTMrK2dLWXVMRm9aa1ZSb1hvemgyTERhR0lyWHMxL2JncnFONExVaUtBb1JyL3JKckxtRkhMRS9YQ2p5QWhtRFFXVEZWZjNmTXcxb2lPNXUyZUxENkhZeUpWZEpzcXU1bHNYdVpIeVBXc01yTTFDSmd6WHcyN2pTUnRBcEIwbXcwNE9KQ2FCVDczcmYrOVR1cXAxdzI0M1hVYmwwYXMydHZLOTJDRDZQb0FKVWh3a1pqTzdEVFFyY3ovRE9XcHFCUmxnUXlVQ3k5a0ZYZ2lSVS9VbUYrTlhUYkRzSmQrMDQ3ZlB3YUNJYm9GL1RCcFhXTHBEdFNpUXhldHlPSGE1R2EwVHVRaUlJaEYxV1VzRlNOcDBsTEI5YitEdFI3RHg4MEU2UW44NGptZXZ3RXA0MUZxZWF6M2psZzhsRGxZOEsxbmJRMUl1Q3U5c1BidnpEMHUxL3lMWkZlWUZsV0l2ejhRWUdmTkQzZ2F6WGhKTjFtYVY5RHZrelcybE50ajZDZW5ZaDZta1pXZWdIZ3h3RWcrR2ZwTkZlTTl1SmZ3TnlFNlgxYzRzSktCMTR5ZHVZMzF4UDRFelg4Z0MzTW5sNlZLOGlSN1JQajZUUnpib0RWaDNsanpMUWwvZ2RyUGU5ZWZrMjdycTk1ZmZ3eHYiLCJtYWMiOiJhMjQ0M2ZmMDYxNDAwNmM0NGNmNDA2ZGU5MjZkN2Q1YzUyMWQ4NDA5NGVjOGE0NzU2YWM5ZTFlZWE0NWI2NzRjIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:38:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkFCZ2M2L05JU0J6VmUydGdDSVlMd1E9PSIsInZhbHVlIjoid2hDa3VxMmpTNC84ek1CMU9NM1lwNnd2ZmZWU3lNZHA5anl4RVpGcHp4cEEyUWswRHRsVXVMT0Y4bng2cDNvS3ZUWFpOMEFOaWlWRG1UVmtiaEVlS1hZMWVoT1Z3ckJZcFMwY0cyK1N3b3pFTlRTUXU1YVQxckhsTzBLanFLZnhUOFJEaENCenFvQnhhdFJrLzdzdks5TDFBekNlNSszdHJJUjZ2N1lPWVRDeWhsM2Z6a1JWYUZuVXE0TUlrSXRCSmVDM040T0ZKbEtVcWUrU2dSQ0hlYmduNnFqWE1XcFdJSUs2R3l1UjhuSzhxcUdCZDliQVBiRkZ6ZkVFWElJcm5CVHozYzF5czJhSlhDZitVNmR0aEN3bklxbDgzWHlqOHB4dHhCayt0WUFnOE51elBwQStMNm96L0RwUWI2SGZxcThocThPVGdMU1pvZHdwRGUwaWpjYWVlbVhUMkF4Y0IvRUUyS0VGYmdmc0VoOHRxR0diVHJ6SDNhOUplUWN2UUYzUVRhTkMwcXdxTGtYaDBsaFd6TVVWNGNXUnJ3cFJNWnhSOVFRazdYa0xxWndJd1lFWjAvbXFSSzN6V3gyWjZtemo0VXprV3E1djhSS2FERlVleVFEZS9BOVU5Z1hKTWROK3E4R09KOXVtTlE5TjEybFNQSTVXVDI5d0NRTUkiLCJtYWMiOiIxNjdhMGIwZTZmMmM5NDUwODg1ZDYwODFiZTllODk2NDYyMWU2ZDVhMzdjODBiYWFhMTQ3ZWM1YWQwYjczYTYyIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:38:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkR3djA1bWVRQkxhazY2MWMxOG9xOEE9PSIsInZhbHVlIjoiTkhiQldYc3Y2ZWxYcTJoMkdKVTMrK2dLWXVMRm9aa1ZSb1hvemgyTERhR0lyWHMxL2JncnFONExVaUtBb1JyL3JKckxtRkhMRS9YQ2p5QWhtRFFXVEZWZjNmTXcxb2lPNXUyZUxENkhZeUpWZEpzcXU1bHNYdVpIeVBXc01yTTFDSmd6WHcyN2pTUnRBcEIwbXcwNE9KQ2FCVDczcmYrOVR1cXAxdzI0M1hVYmwwYXMydHZLOTJDRDZQb0FKVWh3a1pqTzdEVFFyY3ovRE9XcHFCUmxnUXlVQ3k5a0ZYZ2lSVS9VbUYrTlhUYkRzSmQrMDQ3ZlB3YUNJYm9GL1RCcFhXTHBEdFNpUXhldHlPSGE1R2EwVHVRaUlJaEYxV1VzRlNOcDBsTEI5YitEdFI3RHg4MEU2UW44NGptZXZ3RXA0MUZxZWF6M2psZzhsRGxZOEsxbmJRMUl1Q3U5c1BidnpEMHUxL3lMWkZlWUZsV0l2ejhRWUdmTkQzZ2F6WGhKTjFtYVY5RHZrelcybE50ajZDZW5ZaDZta1pXZWdIZ3h3RWcrR2ZwTkZlTTl1SmZ3TnlFNlgxYzRzSktCMTR5ZHVZMzF4UDRFelg4Z0MzTW5sNlZLOGlSN1JQajZUUnpib0RWaDNsanpMUWwvZ2RyUGU5ZWZrMjdycTk1ZmZ3eHYiLCJtYWMiOiJhMjQ0M2ZmMDYxNDAwNmM0NGNmNDA2ZGU5MjZkN2Q1YzUyMWQ4NDA5NGVjOGE0NzU2YWM5ZTFlZWE0NWI2NzRjIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:38:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-708729374\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-166105790 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-166105790\", {\"maxDepth\":0})</script>\n"}}