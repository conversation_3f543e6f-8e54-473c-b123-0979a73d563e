{"__meta": {"id": "X0f13138edebf071926c1ae685777ff38", "datetime": "2025-07-29 06:33:30", "utime": **********.671614, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753770809.536842, "end": **********.671667, "duration": 1.1348249912261963, "duration_str": "1.13s", "measures": [{"label": "Booting", "start": 1753770809.536842, "relative_start": 0, "end": **********.563938, "relative_end": **********.563938, "duration": 1.0270957946777344, "duration_str": "1.03s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.56396, "relative_start": 1.****************, "end": **********.671671, "relative_end": 3.814697265625e-06, "duration": 0.****************, "duration_str": "108ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2999\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1805 to 1811\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1805\" onclick=\"\">routes/web.php:1805-1811</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "a3oNPu6kIOhOKuyMxxNGjsXFZbItobTGctaSyqzf", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-1855772769 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1855772769\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-728865402 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-728865402\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1525983297 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1525983297\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1467090062 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1467090062\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-770505784 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-770505784\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-20528212 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 06:33:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InkrQ2lHUGZSNnA4SzloRDEwUm0zQ2c9PSIsInZhbHVlIjoiN240WWkxOGhEZXFlRTAySnFrbEpQMWNvSHJHdk41SXBDNjcrU1ZYSXVCNEc3b0xmQTl0UzdHUVkxMmtQVXNLNjNhNXcrd0VtVzc4S0hJNEMxNU1hQmNHYWhMYk01emp6L0NpUGR4ejJWa0NNdUZWQWFGWENnemtiWmh3RTArbnNwV3RyQjhpZU9vY3F3WWZPTnFmOUVIRGNHSjMwV2NxQ2JXSTFOaGRxQ3JVOW1DN0RlMnRDaDVEakZvbWhtb0JqbGp0OE4yK2VPdEZCSGFhZHpIcXVjUzdhSC9Gd1Y0TGpISmFoaThKKzdmN044QUZFOEp5S2hWaytXREVNYkIxTGNOTTF2dDkwKzFDZ3RsaXRoMk5nNkRoeVREUFBiWjBQeHNOQUE5d2FHS3JYS2JGeExHRWlTK01oblRhRCtxRnFCdy9ibXl0S1pYUy9NcDNCNUpBeEx5ckdlUUVTWFhwRmR6WDJQcEFUSERMZjREcGZreWhFTUlnV2gzQkRlQVd0UklCb0VHbmhUbGJnVGwxWnRya3A2S3ptSS9SSFNMa1FHMk1HcmZvNVM5QjhEMnhxaVZ3U044TFNNeS9oeFBPT0RxekNsSnRkbVBjM0R3NS90QlVuN3BTWUFkdlVDMXF6L2tIQWdvZVBDc1k4bXZRVjhaTDRyQjAzR2FBZExUWkEiLCJtYWMiOiIzMmU5ODgyMTNkZDBkMzA2Njg4NWVlNWUzODkxZjgzZGFkNDYyMDA0NmMxOTdhODJmZTQyM2YzNjQ3MmExMTM4IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:33:30 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImJQaTdtdDBEYks4L01UbWpDTHBOU2c9PSIsInZhbHVlIjoia2lYKzlDOFRkYWpRdndIekNYVmlBME9rWXhDeVVjT01lT1l0WVp3a2laN2xYZW9JN0NDUENFeDdjUkpLeklVczdlVVJiZ2ovZW5jMmp1ZkZFZkdDM1crRG96K3RKZTVGd0JFVU9EQmNXekZRU3lacVpabUJLanV6M2JNazhGY2VGY0hDQis3SjhybFJraHQ0WElraWk3TkpXdW9QMnY5NjJiR0lwKzVUK3p1ZzU5MzFiWUpqU3dGV1h6Y2xtY2sxRUNjcGdXdUgvemdMOFpoS3c4Q2o1eFpZNmNxSHdPSmlMbnVHcHYzNzc3Y0hzZW9pbktNSGVkdFRKSTB6dnl1VWlXSVRJcFlVOFFXcHEzdVIwVThoK3Yvb0s5RCtkbHYyZGJnVDkrUVg3MDZrY2NXamlRczJES1dreXMyMFFmcHVtVnFBMnZaVXVDR0VWR3VyN1p0UmVxdGJZaWN3RHZ2dzdRZElBVXJSeFpmcTRjb0x4SVFZRnJkUG1xaTEyTGs4aGM0ajZqbXlkMFlUL3RNcHlnWDdnelBuc1RCbzZJWTQ3aThpR3dxWnBXRjRNQ0VyQ2NUbG9UeGpNN3UvR1dNZXFCWjVRVnpiNVZHNmVQbEt2WDFNaU1VUDJlZEpObFFqaHNlT2NBZFYwcGlRaWcybTdTK3AwbHB2bmxJR0hVMGMiLCJtYWMiOiJiODE3NDY3OTI2MmQyY2Y2MDFiNjYyOTc0YWE2MDZkMzg1NDU2NTlhYzcxNjhmMzgxMDI0NTYzNDYzYWYyYmU4IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:33:30 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InkrQ2lHUGZSNnA4SzloRDEwUm0zQ2c9PSIsInZhbHVlIjoiN240WWkxOGhEZXFlRTAySnFrbEpQMWNvSHJHdk41SXBDNjcrU1ZYSXVCNEc3b0xmQTl0UzdHUVkxMmtQVXNLNjNhNXcrd0VtVzc4S0hJNEMxNU1hQmNHYWhMYk01emp6L0NpUGR4ejJWa0NNdUZWQWFGWENnemtiWmh3RTArbnNwV3RyQjhpZU9vY3F3WWZPTnFmOUVIRGNHSjMwV2NxQ2JXSTFOaGRxQ3JVOW1DN0RlMnRDaDVEakZvbWhtb0JqbGp0OE4yK2VPdEZCSGFhZHpIcXVjUzdhSC9Gd1Y0TGpISmFoaThKKzdmN044QUZFOEp5S2hWaytXREVNYkIxTGNOTTF2dDkwKzFDZ3RsaXRoMk5nNkRoeVREUFBiWjBQeHNOQUE5d2FHS3JYS2JGeExHRWlTK01oblRhRCtxRnFCdy9ibXl0S1pYUy9NcDNCNUpBeEx5ckdlUUVTWFhwRmR6WDJQcEFUSERMZjREcGZreWhFTUlnV2gzQkRlQVd0UklCb0VHbmhUbGJnVGwxWnRya3A2S3ptSS9SSFNMa1FHMk1HcmZvNVM5QjhEMnhxaVZ3U044TFNNeS9oeFBPT0RxekNsSnRkbVBjM0R3NS90QlVuN3BTWUFkdlVDMXF6L2tIQWdvZVBDc1k4bXZRVjhaTDRyQjAzR2FBZExUWkEiLCJtYWMiOiIzMmU5ODgyMTNkZDBkMzA2Njg4NWVlNWUzODkxZjgzZGFkNDYyMDA0NmMxOTdhODJmZTQyM2YzNjQ3MmExMTM4IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:33:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImJQaTdtdDBEYks4L01UbWpDTHBOU2c9PSIsInZhbHVlIjoia2lYKzlDOFRkYWpRdndIekNYVmlBME9rWXhDeVVjT01lT1l0WVp3a2laN2xYZW9JN0NDUENFeDdjUkpLeklVczdlVVJiZ2ovZW5jMmp1ZkZFZkdDM1crRG96K3RKZTVGd0JFVU9EQmNXekZRU3lacVpabUJLanV6M2JNazhGY2VGY0hDQis3SjhybFJraHQ0WElraWk3TkpXdW9QMnY5NjJiR0lwKzVUK3p1ZzU5MzFiWUpqU3dGV1h6Y2xtY2sxRUNjcGdXdUgvemdMOFpoS3c4Q2o1eFpZNmNxSHdPSmlMbnVHcHYzNzc3Y0hzZW9pbktNSGVkdFRKSTB6dnl1VWlXSVRJcFlVOFFXcHEzdVIwVThoK3Yvb0s5RCtkbHYyZGJnVDkrUVg3MDZrY2NXamlRczJES1dreXMyMFFmcHVtVnFBMnZaVXVDR0VWR3VyN1p0UmVxdGJZaWN3RHZ2dzdRZElBVXJSeFpmcTRjb0x4SVFZRnJkUG1xaTEyTGs4aGM0ajZqbXlkMFlUL3RNcHlnWDdnelBuc1RCbzZJWTQ3aThpR3dxWnBXRjRNQ0VyQ2NUbG9UeGpNN3UvR1dNZXFCWjVRVnpiNVZHNmVQbEt2WDFNaU1VUDJlZEpObFFqaHNlT2NBZFYwcGlRaWcybTdTK3AwbHB2bmxJR0hVMGMiLCJtYWMiOiJiODE3NDY3OTI2MmQyY2Y2MDFiNjYyOTc0YWE2MDZkMzg1NDU2NTlhYzcxNjhmMzgxMDI0NTYzNDYzYWYyYmU4IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:33:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-20528212\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">a3oNPu6kIOhOKuyMxxNGjsXFZbItobTGctaSyqzf</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}