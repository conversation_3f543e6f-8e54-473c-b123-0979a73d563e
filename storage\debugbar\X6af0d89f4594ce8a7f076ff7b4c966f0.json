{"__meta": {"id": "X6af0d89f4594ce8a7f076ff7b4c966f0", "datetime": "2025-07-29 06:37:49", "utime": **********.971664, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.019457, "end": **********.971699, "duration": 0.9522418975830078, "duration_str": "952ms", "measures": [{"label": "Booting", "start": **********.019457, "relative_start": 0, "end": **********.881895, "relative_end": **********.881895, "duration": 0.8624379634857178, "duration_str": "862ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.881968, "relative_start": 0.****************, "end": **********.971702, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "89.73ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2999\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1805 to 1811\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1805\" onclick=\"\">routes/web.php:1805-1811</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "FC6bKT1mfHTFPgM6XaywjL3aPZjepOlfQHZQPMM7", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-2073549861 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2073549861\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1708367053 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1708367053\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-337296520 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-337296520\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1138263259 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1138263259\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-407774932 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-407774932\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-550689325 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 06:37:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IncrQ2RrVThVM0ZxUmRUejE5UmFnZ0E9PSIsInZhbHVlIjoiTm5Xd1Z5dng5cUNXV0ZMc1l4SklBV2F0UUpDbk00Ymc4ZTUyRGpnNlJYdVNtbUtOenZsWStlOHlyeFFLbktaTWo5b0lZQ1VVQkpaaEo4MjJLYmF2L3JMMGRqOUtUUm1wZFVQQld5ZXZRQkdrVi9UV0lqRWl1Y1NGRk03RmdSZE5hK1FWeVk5RkVwQUxtWEZWeTZkTmZWYVI5WkljTkUycUxueG9rS0EwcXF3emU4amJFMDVNZ25LKy9XZ0ltejlDQWFPR3Z3T3MrKytlSUJ5RXpyRThFQTIwa09yNDBEZzVnTXJ2L1VTWUlRcHcvSUcrZTJLUVVsNGoxQThGRVovMFRrQkpaeDJMSHRuUXNQc2RoKzdlU0prYkVuRnExSVl4Y1V1SWFCaXFwclU5ZXRwZWhjcllMRUs1ZUJPRUJrSHhYRzVSMmg5VEdGdVhvaE4rYW1SV25jR1M0ZHBsamduU3FGT284WVlXTmNyeTc3TmZKNUJxdldyNlJ5eGhtbFp5VWsrWHlKWEVsZjF5UUFHRVZzcSs2R3F3c2dWaTNYc2QyWHhmN1ZsUGJVWmEveThpNmhFZnkrU3Zyamp4aXdjdEl0bU5HUmwvd21tT0swNFFlWHRoK2xmNnVrR1ZmV0JldCs5U0JnWXlCWXUwMXdnL2pWS2tId3hFOHdzWWI4UTQiLCJtYWMiOiIzY2ZlODU4NGVmMzY3NDBhOTA2YTY3ZTBmZWUwNGQ2MWMwZTg5NWU3MDhlNjQzMDliZGZhZGJhZWI4ODBhYmZlIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:37:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkdPRHUwOUxNOEV6RFdZLytoU3BNYlE9PSIsInZhbHVlIjoibXFzbFY2RFJDTVNYeUVyU1Ird3lvWjl4UWtsdFZpRTRySWpSTEVvd3N1bit1QXFyVjlBWGxDNjR1TVE1TGJqK3BZeitENDg0eGtVWGFFQ09WUGtPYjB5MTlTK05COWRyUXhUSjhLV3BlSUVWdVBaUjZRUXRWWWpkZTJDZ1R0RFdqZnNXSHloUDZQNFZHZjg0NFZRTkdtZnRWZ1B0UGJtTHZGSGNWUmFicURIdExCd3BCNUhmZFplVlBXSkU3aitrWlYvSnVvNkFBdTFYQXNva1hGQW9iY0ZYdGpQR3dEbnNJQ1M2Vld2bXpJR1RYS1p1dHJlekY1Y0VBRUNRaUtESjU3bDJPV1lkSUpMUlZYY0M5NUpoZG83b0VGU2ZpNHd2TDczeTdNVXlBcS92bWVPWkhYMkJERkZYU2o4QTc5Q1VrNy9jUjVTNm1MVGczdnRFZ2d1U2w2ZzBYRmhFZUcvZW1KdkJnNXQ2VWJ5SlJUSXdDR0ZieGh3eDlFQWhLcnVUa3RZUGRCRDBvbDlzNmY2byt3VUxxN2JybHhGWlVyemIxUmp1RWVEK2RtbGZ5U1lXcm9qWGNFaW5na2I2cGdjSmRCNitNSnFvdWduSWE0eVR2VVpNMU1wTlZodjh3N0hlZE1ZNmJhbzZaMVdnUysvK1BGK3pnZ0tCOWZZaElLR3EiLCJtYWMiOiIyNWQyZjg1NDkxNWUwZmU4Nzg1ZGRhOGYxZDQ1OTZjNzc0NmYxN2UzMzMzNTFiMDQ0Yjk1MTYyM2JiNzk2OGQ2IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:37:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IncrQ2RrVThVM0ZxUmRUejE5UmFnZ0E9PSIsInZhbHVlIjoiTm5Xd1Z5dng5cUNXV0ZMc1l4SklBV2F0UUpDbk00Ymc4ZTUyRGpnNlJYdVNtbUtOenZsWStlOHlyeFFLbktaTWo5b0lZQ1VVQkpaaEo4MjJLYmF2L3JMMGRqOUtUUm1wZFVQQld5ZXZRQkdrVi9UV0lqRWl1Y1NGRk03RmdSZE5hK1FWeVk5RkVwQUxtWEZWeTZkTmZWYVI5WkljTkUycUxueG9rS0EwcXF3emU4amJFMDVNZ25LKy9XZ0ltejlDQWFPR3Z3T3MrKytlSUJ5RXpyRThFQTIwa09yNDBEZzVnTXJ2L1VTWUlRcHcvSUcrZTJLUVVsNGoxQThGRVovMFRrQkpaeDJMSHRuUXNQc2RoKzdlU0prYkVuRnExSVl4Y1V1SWFCaXFwclU5ZXRwZWhjcllMRUs1ZUJPRUJrSHhYRzVSMmg5VEdGdVhvaE4rYW1SV25jR1M0ZHBsamduU3FGT284WVlXTmNyeTc3TmZKNUJxdldyNlJ5eGhtbFp5VWsrWHlKWEVsZjF5UUFHRVZzcSs2R3F3c2dWaTNYc2QyWHhmN1ZsUGJVWmEveThpNmhFZnkrU3Zyamp4aXdjdEl0bU5HUmwvd21tT0swNFFlWHRoK2xmNnVrR1ZmV0JldCs5U0JnWXlCWXUwMXdnL2pWS2tId3hFOHdzWWI4UTQiLCJtYWMiOiIzY2ZlODU4NGVmMzY3NDBhOTA2YTY3ZTBmZWUwNGQ2MWMwZTg5NWU3MDhlNjQzMDliZGZhZGJhZWI4ODBhYmZlIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:37:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkdPRHUwOUxNOEV6RFdZLytoU3BNYlE9PSIsInZhbHVlIjoibXFzbFY2RFJDTVNYeUVyU1Ird3lvWjl4UWtsdFZpRTRySWpSTEVvd3N1bit1QXFyVjlBWGxDNjR1TVE1TGJqK3BZeitENDg0eGtVWGFFQ09WUGtPYjB5MTlTK05COWRyUXhUSjhLV3BlSUVWdVBaUjZRUXRWWWpkZTJDZ1R0RFdqZnNXSHloUDZQNFZHZjg0NFZRTkdtZnRWZ1B0UGJtTHZGSGNWUmFicURIdExCd3BCNUhmZFplVlBXSkU3aitrWlYvSnVvNkFBdTFYQXNva1hGQW9iY0ZYdGpQR3dEbnNJQ1M2Vld2bXpJR1RYS1p1dHJlekY1Y0VBRUNRaUtESjU3bDJPV1lkSUpMUlZYY0M5NUpoZG83b0VGU2ZpNHd2TDczeTdNVXlBcS92bWVPWkhYMkJERkZYU2o4QTc5Q1VrNy9jUjVTNm1MVGczdnRFZ2d1U2w2ZzBYRmhFZUcvZW1KdkJnNXQ2VWJ5SlJUSXdDR0ZieGh3eDlFQWhLcnVUa3RZUGRCRDBvbDlzNmY2byt3VUxxN2JybHhGWlVyemIxUmp1RWVEK2RtbGZ5U1lXcm9qWGNFaW5na2I2cGdjSmRCNitNSnFvdWduSWE0eVR2VVpNMU1wTlZodjh3N0hlZE1ZNmJhbzZaMVdnUysvK1BGK3pnZ0tCOWZZaElLR3EiLCJtYWMiOiIyNWQyZjg1NDkxNWUwZmU4Nzg1ZGRhOGYxZDQ1OTZjNzc0NmYxN2UzMzMzNTFiMDQ0Yjk1MTYyM2JiNzk2OGQ2IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:37:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-550689325\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1874495848 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FC6bKT1mfHTFPgM6XaywjL3aPZjepOlfQHZQPMM7</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1874495848\", {\"maxDepth\":0})</script>\n"}}