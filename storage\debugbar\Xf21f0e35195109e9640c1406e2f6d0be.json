{"__meta": {"id": "Xf21f0e35195109e9640c1406e2f6d0be", "datetime": "2025-07-29 06:30:03", "utime": **********.643375, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753770601.120293, "end": **********.643534, "duration": 2.5232410430908203, "duration_str": "2.52s", "measures": [{"label": "Booting", "start": 1753770601.120293, "relative_start": 0, "end": **********.3213, "relative_end": **********.3213, "duration": 2.201007127761841, "duration_str": "2.2s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.321369, "relative_start": 2.***************, "end": **********.643543, "relative_end": 9.059906005859375e-06, "duration": 0.***************, "duration_str": "322ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2999\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1805 to 1811\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1805\" onclick=\"\">routes/web.php:1805-1811</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "7K8oyQVniesvH9pXmSfpTkqQZOTrmSzUKhAtJRDA", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-824991251 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-824991251\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-648349490 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-648349490\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-599956908 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-599956908\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1623481703 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1623481703\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-169275885 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-169275885\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-421819010 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 06:30:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InpRMkRUUjNQelJrUkVtVGpWdFczUkE9PSIsInZhbHVlIjoiK0ZFL1FkMU1PZFBqTVo1RytORndjYkc0QXpTc254dy9VTkEzalpmRXZTNldzRWkrbXVHUU15aG1RVkFtaVBRajM4SysvMUNka2VWTjJ0QnNWblVwZXhLYXkxS3Z2TUNyZkVHeVY1RmdrbHVZdGJQbG9ZbnZicXdvMWxINHhEUTlQOEVrMll0QU1jdWFsTkFKcFA0azRBSzVMVitJWnFIVTc2d0d4V3pIRTBNK2pkOE1RNEJHZ1V3K3B0QXNETi83dGRnNWlJRE9YS1Zwd2xLMVhWZXYwUkdYSXQ1bjVpZDZDVS9JVWJGN1pVSzZXNEk3c25SR1JUUDF6NnBmcUxBbVhoYy95VVlUcHlEZlFiV2xBWkQ3VEZBS1ZkQyt6VkhFRXlsRHZJTkxJNjBueVBnYjJtSDI5Tzc3azlzYUpoYXNRZ1hIV2RqcEc5VUhob2NCZWEwZnJMME4zT01mdW03ZnFjZWp4UGhUYlZIYmRqQnJILzZGZm1VRThiR0kzS21LekRJY2JNcjBXeE5QMGdpVHE5bDlnUEJaNDkzdWMrRFpxdnB0dXF5ZHBONUpiQkZmWG9vbE9mcDdHWnJYbEppZHltbHlLRS9HVG1LemdFS256RFhOYnRIaTZaZFlHdXM5MHlmeElHRVR5dTR6Y056MXl2RElCN0pvNEN4eXhBZ1ciLCJtYWMiOiIwNWEwZmFhYTZkNmU5Nzk3MmMxNThlNTcyNjRhZWQwNTNhMTFiMzEwY2UxMTVjODZmMWUzNzBiM2ViOWFkZmI1IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:30:03 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlZMV2s0Z3R0RmlBZzZDL2tPbk16SWc9PSIsInZhbHVlIjoiN25kejJjWVN1dVJHMDI4cCtMWXltTFlOUUlUN1FkaGo1anE3VUJVbkhXNlJVVS93ODZweVhLTVhVY2pYcUthRUNkZFJYRWE3bDhyY3Z5M3ExNFVCM05rMmYzYXdVSTlEQWEvc3ZWZlIySWlaQVFIbHdOREdZc3lST1F5NTUwSEFkU00yNjRrRzZ6MHBNQWdhYWpVcG5VNXpsbjdFaUh4RFYrS1dTYXJhM09jUzFYVlptRFVDL1gzVEsyakZmOFh4RElkLzc2SkI0czd6Q1BqVGFUSFNEZlYzUlgrSFg3QmZRTS91c1ljTFA0VmVUbjdQNnEweDdwNDZCaUpING85ZjBaUll0dFRzM3ZhOWVzQ0hVbmlFbFJvRmoxdUduTzVybExkOHlkd3BXNU9TSE9FKzRlclY3d05Cek16MnlsMWloaE1odVBqQ01yS2s5R2E0d3JNZmdyNnJlQ2pqWnFmTzIwODQySGhuaWdGTUlPcW5VOWE2NVFWZTBlSExRTTZQaHZpV2RXL2JRRmswRHVLa3lGSzdualplYis3c0xDNzE5cHNvSGRRY2N1OE1WdGgvZU1jdGdPeFNWbkR0NmtHSmVFVVpnVWw2T1VXdnhncXU5aE14M05WMDJyWTg2VDdjT1Y0aTV5SnFUUlhvblFQYUpTNTltYUlaMWV6Q05iMkYiLCJtYWMiOiI0ZDJkNWIwODlhODc3YTdhMzdkN2RhZGMwYjVlYjQwNDQxMWQ3ZDJlNjVmNjUxNjg3M2IyMGNiZmE4YzMzODg3IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:30:03 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InpRMkRUUjNQelJrUkVtVGpWdFczUkE9PSIsInZhbHVlIjoiK0ZFL1FkMU1PZFBqTVo1RytORndjYkc0QXpTc254dy9VTkEzalpmRXZTNldzRWkrbXVHUU15aG1RVkFtaVBRajM4SysvMUNka2VWTjJ0QnNWblVwZXhLYXkxS3Z2TUNyZkVHeVY1RmdrbHVZdGJQbG9ZbnZicXdvMWxINHhEUTlQOEVrMll0QU1jdWFsTkFKcFA0azRBSzVMVitJWnFIVTc2d0d4V3pIRTBNK2pkOE1RNEJHZ1V3K3B0QXNETi83dGRnNWlJRE9YS1Zwd2xLMVhWZXYwUkdYSXQ1bjVpZDZDVS9JVWJGN1pVSzZXNEk3c25SR1JUUDF6NnBmcUxBbVhoYy95VVlUcHlEZlFiV2xBWkQ3VEZBS1ZkQyt6VkhFRXlsRHZJTkxJNjBueVBnYjJtSDI5Tzc3azlzYUpoYXNRZ1hIV2RqcEc5VUhob2NCZWEwZnJMME4zT01mdW03ZnFjZWp4UGhUYlZIYmRqQnJILzZGZm1VRThiR0kzS21LekRJY2JNcjBXeE5QMGdpVHE5bDlnUEJaNDkzdWMrRFpxdnB0dXF5ZHBONUpiQkZmWG9vbE9mcDdHWnJYbEppZHltbHlLRS9HVG1LemdFS256RFhOYnRIaTZaZFlHdXM5MHlmeElHRVR5dTR6Y056MXl2RElCN0pvNEN4eXhBZ1ciLCJtYWMiOiIwNWEwZmFhYTZkNmU5Nzk3MmMxNThlNTcyNjRhZWQwNTNhMTFiMzEwY2UxMTVjODZmMWUzNzBiM2ViOWFkZmI1IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:30:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlZMV2s0Z3R0RmlBZzZDL2tPbk16SWc9PSIsInZhbHVlIjoiN25kejJjWVN1dVJHMDI4cCtMWXltTFlOUUlUN1FkaGo1anE3VUJVbkhXNlJVVS93ODZweVhLTVhVY2pYcUthRUNkZFJYRWE3bDhyY3Z5M3ExNFVCM05rMmYzYXdVSTlEQWEvc3ZWZlIySWlaQVFIbHdOREdZc3lST1F5NTUwSEFkU00yNjRrRzZ6MHBNQWdhYWpVcG5VNXpsbjdFaUh4RFYrS1dTYXJhM09jUzFYVlptRFVDL1gzVEsyakZmOFh4RElkLzc2SkI0czd6Q1BqVGFUSFNEZlYzUlgrSFg3QmZRTS91c1ljTFA0VmVUbjdQNnEweDdwNDZCaUpING85ZjBaUll0dFRzM3ZhOWVzQ0hVbmlFbFJvRmoxdUduTzVybExkOHlkd3BXNU9TSE9FKzRlclY3d05Cek16MnlsMWloaE1odVBqQ01yS2s5R2E0d3JNZmdyNnJlQ2pqWnFmTzIwODQySGhuaWdGTUlPcW5VOWE2NVFWZTBlSExRTTZQaHZpV2RXL2JRRmswRHVLa3lGSzdualplYis3c0xDNzE5cHNvSGRRY2N1OE1WdGgvZU1jdGdPeFNWbkR0NmtHSmVFVVpnVWw2T1VXdnhncXU5aE14M05WMDJyWTg2VDdjT1Y0aTV5SnFUUlhvblFQYUpTNTltYUlaMWV6Q05iMkYiLCJtYWMiOiI0ZDJkNWIwODlhODc3YTdhMzdkN2RhZGMwYjVlYjQwNDQxMWQ3ZDJlNjVmNjUxNjg3M2IyMGNiZmE4YzMzODg3IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:30:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-421819010\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-323447730 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7K8oyQVniesvH9pXmSfpTkqQZOTrmSzUKhAtJRDA</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-323447730\", {\"maxDepth\":0})</script>\n"}}