{"__meta": {"id": "X711af3043a17f1d1e9e94a7ed23682b3", "datetime": "2025-07-29 07:08:59", "utime": **********.217716, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753772937.813391, "end": **********.217752, "duration": 1.4043610095977783, "duration_str": "1.4s", "measures": [{"label": "Booting", "start": 1753772937.813391, "relative_start": 0, "end": **********.066088, "relative_end": **********.066088, "duration": 1.2526969909667969, "duration_str": "1.25s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.066118, "relative_start": 1.2527270317077637, "end": **********.217755, "relative_end": 3.0994415283203125e-06, "duration": 0.15163707733154297, "duration_str": "152ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45781888, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=58\" onclick=\"\">app/Http/Controllers/DashboardController.php:58-75</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.005860000000000001, "accumulated_duration_str": "5.86ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 341}, {"index": 21, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 26}], "start": **********.1647391, "duration": 0.00487, "duration_str": "4.87ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 0, "width_percent": 83.106}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.188643, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 83.106, "width_percent": 16.894}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-1686974039 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1686974039\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1498246814 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1498246814\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-378969113 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-378969113\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2000428209 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6InU3d3dFbitXczF4NWFWY0k1VmI0RHc9PSIsInZhbHVlIjoiVXVtbStvYStmamVqSDFoakpmUmxyVGFzRlN4MXRzNk0xekFkK0k3Mko4OHlDZFowajRMK2drdWI4QkxSZFM5RlR3Y053Mzg3Vnl3Ym1yd2R2eTZkSFRnM2NuVHA0SEpTaVM3TXFqcU9VNmFFdWV1TlZYcmg4UG5ZbDZWd0RVY1AwbTc1UVFZVzU1aWkreDNmS25lYW1uVkw5dlpmNEdoYmJIR0dMYXB5aWhiUnZubFpNSi9HbnNZUmgrZ05hcnd2OWI5bHZXL1dubGEyb0FvSTRySnRNbDhsejF3cG5ZSGl6bWVvNEpEMHRSU0RIaWMyMjBxZ0U4RmpnRmQ5dW44bzFjUmJ3aDgyaTRSdkhlbnZWS2tUU1h3WCtybXhNNXRxV1lhRXZjWmNPTTJjRVdKRzErS3Q4djdoSTROeXpwdDA4N2h1bmNTNXVVS2paUUk2VnRYSzRVb0dzbXphc3A5aFFjMnhqODU1am1zNExjZHpEMWprYk1tTzNEYys1YUpZenNqb0hvTlAxTWxnRjA5aGlWRkZTV1FqblpuN2VmTDJia3JYQkJyMGFzZ0hjU29aOU1hQ3JoeTFIZ3ViQzZSTUgvQUQvQnlWdDBEOHhWcEsvQ0o5WVBnWndBOEl5MWlnVGhEVGRZd1NFV1Jma1ZNQ2VrQ0NOalRQejVkUS9IZ0wiLCJtYWMiOiJjYjExMWI2OWY2Y2U5YjkyZDFjZmQ3MWI0ZTFmY2YzMmVmMmI2ZTg0NDA5MmI5NGFiNTNkNjBkYTJlOWRhOWFiIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Ino3bkJzUzRWSGFGNXdvUDBINTNkeHc9PSIsInZhbHVlIjoiWUZsQUt6QVhUY3ZYVjY2dzd1dUVvWXNTSW03Q3p4VHlYUXFNa0lYVlJ6SlFrczM4QjFsdFFOVUt3RkNLVjVxUEZzRXh2Z0R0WHpLTkRxL0Fqa0laZERmaFNjNnYrdndJL1YxblBYamcxbCtqSTRqMklOdTRKcUtHR0szUnkxc2duRzhYZk5xN09HVzhnSzE4LzJtZ2RUS1dkdGl5cmIzeXpweWRNS3dTZ0xIb2c2YzZKR01CUCs0bDZxUzI5YUNsWm9mVW41Z0JEYVB0Qjl0WTdYNHNsZmNLK2RnOUk3UWZIeTBFVytoWktCOFBFZnRnQVFqMzBPNTlkTGJFZjFNVG1YdTNGdm9EYVlXRWFXOGQ4Y2ZMaXg5K09RcVVzUnNUVERrdmt3V21BT3Z4bjJLT3pHU1ZNRk91RDBBclJXcSsyV2ErMUFpeTVqeUtTMDNsV3pGUHJ2cE5JOXRvOHNjQVRGNUo2TzQyRGcxR1lJRVFhL05vQzhJNTRDNjRvdmJCbmVxK0s1SldXcWllamxKTmdIOTdVd0t4TVM1QVZBVlRSNGF5SURzM2ROQlRsampXMFlRMmZuKzlwVE5iQUhBVHRHUmludjNKLzF1Y2g1WjczQ2JKRnZJV2U4Ylp4ZjVIVG9GS282azNTQzE2RTRobENIbXhoU1U0STlEbFBBTmYiLCJtYWMiOiIwNGZjZmE3MThhYWZmMDY2YTkyM2Q2NWRiOGNhOWQzZjc1ODk1MjkzYzk4MjlmZjg1ZWVhMzAyM2Q3NmUxNjRlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2000428209\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-102260155 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1XDrxTWmYv3gorVrXvmSodhuVHNiE4Wue14QMI4v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-102260155\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1840760130 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 07:08:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InZMcGhZNTRTK1plT3FLd0p0ZTBqdUE9PSIsInZhbHVlIjoiTE92QWRaVlRpSGNpbU8zWStqNDR5Q1RYTlIxeXgxMmcrNGdmZnBqWHJ5L3FNNG8yUXcwM1M2MzlMMlM0ZWVBVW55aGNzMEZDNWV0S3d2Y0lxOGgwUTVBak9TVUw1NmV6N3Q5MUFxK0ZlZ0dRd0VDaFdSUGdQdlJienNoZ0hRT1ZlcWxOb3RQZTVWQnRPektqdzJVOWNBcmcxVFhJKy92UG9zMVVxZSs3U1AwYjNxcSs0S0Z0dHZSaGcydUZMQUFUcFRBV3I5WTZ0R1VSV2ROL0h0aHErWkxGanBhZ2pvTmVOWVZ1aFhWSHN6SUk2ZE5Id21lY2QwMzh2ckhKOWJNU1k0b3FpYlJ5Yk9iMnpwaEo0U2lpdSs2ZlI0VGs1Z2FmK3ZGUDlRS2FMdXFjRGNvbDBtaUVjblpUUll2MlNlY2xISjZmVGdaQ2ZBeW9yQlEvR1hsOXBVWTU3WWhXVGxoV0RzOEhKZzRXVFVNUWtoMEROWlpsblJZMU1BeWx3VGpUZ3lLaHNwMHViVVZ3ZGV1b1FpcTZpcWhLSHJIU3ZlbHNGVEFsczFGYlZPby9yYklRRXY1dmVKK3E1ZEJVN0NPZkFlWGlVQjM4WGcyM3dNWmNna3VBQnhCWUx1dTNIbkhmc1dvTmhYelFLT1E2clQvZzNGbGM3MDlCRVFBTG5YNmEiLCJtYWMiOiJlMDU1MGJmNGEwMDUxZGUyNGNlMmU5NDNmZGIxOWJkYmM1ODExN2I0YTE3Y2Q0MmVjODI4MTRlMGZmMjJiNjQyIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 09:08:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlBhd1V4R2l3bVJiN2xyaktOWnRaM2c9PSIsInZhbHVlIjoiUGJycWNZTDA4ek5MSUUwZzQ0akEzZEdTWmpwK0JyVlh1enhURUtKYzNUY0czRjMvRW0vWldPUkNNVnptazR4c3ZobGJvbnBkWXRWdmtoNEdCMDZqTXZuMjRJNzFHb1YzREgvckRtWUZ1TWFEWWRUSTBrcUl3azRBQ20rd0QzckkweFRncjI5eFBZVVRUOGpBd2Q0dE5BUWpad0Fqem5ZNmM0UTFYc21VR21ubHI1K3VqMndiTTBqdVBQNXlUWWhoa2tqNm5UMDFWaktDYWJQUy9JeHBVbm5xK3ViZU9JQ210U1BIVFJYeEZXY1M3RHRvckplMmRWTDl2dGV5ZXA2RzhXRTArM0tjcnNpYVZBWGFVV3NVaTZlS1FlNzN2aVk2M0JHd203QVJiTkVISXFNU1cxbDZ6VmlyVnhlT1dxUFYyWElyVnpqR0wyVUpRZWtWWnNuVjdZUXA2Q2g5d2Y3b2srbFZ2MkgwcEoyazduK3FzMFhvdDRra0p4VmpJVWVsWVVTTVErcE5KZ2cvN1hhU1BKVVpLNWplbnNhWTBqVk5MNGhjRC9seEZQbzE2Y1BOdUg4QzI1SFA1bGdaNExCTEdDU3FOR2xoT2VzMFEwTUNGNDhzdDFCYUJmTFhnS0V2N2FTQ2NGNG0rS2J5SkludnRwQUNHTENLR1dNUSsvd2giLCJtYWMiOiIzODFlOWZmNDQwNDQxNTMzNjQ0MzYyNTE0OWMxNmVjMWI1M2Y5NTcxYjU0ZTkzYzBhNjhiZThmYzg5YzlkNDhhIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 09:08:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InZMcGhZNTRTK1plT3FLd0p0ZTBqdUE9PSIsInZhbHVlIjoiTE92QWRaVlRpSGNpbU8zWStqNDR5Q1RYTlIxeXgxMmcrNGdmZnBqWHJ5L3FNNG8yUXcwM1M2MzlMMlM0ZWVBVW55aGNzMEZDNWV0S3d2Y0lxOGgwUTVBak9TVUw1NmV6N3Q5MUFxK0ZlZ0dRd0VDaFdSUGdQdlJienNoZ0hRT1ZlcWxOb3RQZTVWQnRPektqdzJVOWNBcmcxVFhJKy92UG9zMVVxZSs3U1AwYjNxcSs0S0Z0dHZSaGcydUZMQUFUcFRBV3I5WTZ0R1VSV2ROL0h0aHErWkxGanBhZ2pvTmVOWVZ1aFhWSHN6SUk2ZE5Id21lY2QwMzh2ckhKOWJNU1k0b3FpYlJ5Yk9iMnpwaEo0U2lpdSs2ZlI0VGs1Z2FmK3ZGUDlRS2FMdXFjRGNvbDBtaUVjblpUUll2MlNlY2xISjZmVGdaQ2ZBeW9yQlEvR1hsOXBVWTU3WWhXVGxoV0RzOEhKZzRXVFVNUWtoMEROWlpsblJZMU1BeWx3VGpUZ3lLaHNwMHViVVZ3ZGV1b1FpcTZpcWhLSHJIU3ZlbHNGVEFsczFGYlZPby9yYklRRXY1dmVKK3E1ZEJVN0NPZkFlWGlVQjM4WGcyM3dNWmNna3VBQnhCWUx1dTNIbkhmc1dvTmhYelFLT1E2clQvZzNGbGM3MDlCRVFBTG5YNmEiLCJtYWMiOiJlMDU1MGJmNGEwMDUxZGUyNGNlMmU5NDNmZGIxOWJkYmM1ODExN2I0YTE3Y2Q0MmVjODI4MTRlMGZmMjJiNjQyIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 09:08:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlBhd1V4R2l3bVJiN2xyaktOWnRaM2c9PSIsInZhbHVlIjoiUGJycWNZTDA4ek5MSUUwZzQ0akEzZEdTWmpwK0JyVlh1enhURUtKYzNUY0czRjMvRW0vWldPUkNNVnptazR4c3ZobGJvbnBkWXRWdmtoNEdCMDZqTXZuMjRJNzFHb1YzREgvckRtWUZ1TWFEWWRUSTBrcUl3azRBQ20rd0QzckkweFRncjI5eFBZVVRUOGpBd2Q0dE5BUWpad0Fqem5ZNmM0UTFYc21VR21ubHI1K3VqMndiTTBqdVBQNXlUWWhoa2tqNm5UMDFWaktDYWJQUy9JeHBVbm5xK3ViZU9JQ210U1BIVFJYeEZXY1M3RHRvckplMmRWTDl2dGV5ZXA2RzhXRTArM0tjcnNpYVZBWGFVV3NVaTZlS1FlNzN2aVk2M0JHd203QVJiTkVISXFNU1cxbDZ6VmlyVnhlT1dxUFYyWElyVnpqR0wyVUpRZWtWWnNuVjdZUXA2Q2g5d2Y3b2srbFZ2MkgwcEoyazduK3FzMFhvdDRra0p4VmpJVWVsWVVTTVErcE5KZ2cvN1hhU1BKVVpLNWplbnNhWTBqVk5MNGhjRC9seEZQbzE2Y1BOdUg4QzI1SFA1bGdaNExCTEdDU3FOR2xoT2VzMFEwTUNGNDhzdDFCYUJmTFhnS0V2N2FTQ2NGNG0rS2J5SkludnRwQUNHTENLR1dNUSsvd2giLCJtYWMiOiIzODFlOWZmNDQwNDQxNTMzNjQ0MzYyNTE0OWMxNmVjMWI1M2Y5NTcxYjU0ZTkzYzBhNjhiZThmYzg5YzlkNDhhIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 09:08:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1840760130\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1941721638 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1941721638\", {\"maxDepth\":0})</script>\n"}}