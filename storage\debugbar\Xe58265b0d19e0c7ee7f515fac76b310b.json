{"__meta": {"id": "Xe58265b0d19e0c7ee7f515fac76b310b", "datetime": "2025-07-29 06:31:48", "utime": **********.925251, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753770707.64329, "end": **********.925305, "duration": 1.2820148468017578, "duration_str": "1.28s", "measures": [{"label": "Booting", "start": 1753770707.64329, "relative_start": 0, "end": **********.851346, "relative_end": **********.851346, "duration": 1.2080559730529785, "duration_str": "1.21s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.85137, "relative_start": 1.****************, "end": **********.925308, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "73.94ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2999\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1805 to 1811\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1805\" onclick=\"\">routes/web.php:1805-1811</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "UjUQ3NSjsfxYXjXar02iivdJ5YNzIqknuABt5l2G", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-842614205 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-842614205\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-304214682 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-304214682\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1911822558 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1911822558\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1780110480 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1780110480\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-162996444 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-162996444\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1538278334 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 06:31:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InJkVFR6NEVmODVBVGxWSzd3NjdETWc9PSIsInZhbHVlIjoiKzRndjVDeUpyTjdHYUJySk1YQ21mSVkvU1hxVUZ5eDhUamNMTTM4NVpXTTJlL01abkhzZ21HR0FuV2N2OXBDQisrNUdUQk1mVlJ0ek5YVjJLeCtZVTI5WFgzNjN5ZTUvZ0VNTXFoM3ZRYjZyc05xVVY4V0VyelBNNWU3ZXNvR3MwQlNETStGQnUyRnAzc05jbDBzL1FlbjBQbStRay9LWStiWDd2eGVhYnBJb0RvRk5ZWEpnQlRKWUpKcENwN3BqOXowQUFvNFU0czFrNVpLR3FCRkVXRml1Q1gxd0lZSWJRTzJEZmNrMGFjNmdoYmx6dkZlWGhBeDZoY3NmSkdQRnhEWXlNOC9xL0Z5Wk41R0NZWktOTFpIUU9YWEkybFgzQkk4TTlqUDM5NHVqWlBiQ3dZSUovNTZKeG9xWDk0NnRwQU1CRDIxVDBaRGdzMEVPQ1VhVFl1emRQQjcvNnlRRXpnRVRvV3hac0FHUWFkY2Q3djVmOGlLQzlOZjJLdmpaT2ROZGdzcHQ1a3ZIMldFeEJ1d0puZHJmYXpFRlZ3aElqVlYyNE82cXdnUDRXNTEvdGNSUlRmaVpaZ3I1Y1c0SVZCMHM2SUludkRjYlRwdW1aZFVNc1FoNGUyRHQrZWZWc3FTa1JOdVpRYmhKQjhwb0VDV0R2SktONlpLV0dlcWciLCJtYWMiOiI2ZmEyMGI5YjVkNTk3OWM1YjQ2NzUzNTUyY2VhYjc5ZmQ0NTQzMzY0Yzg1ZGE0NjY2Mzc3MDg4N2I0YmFhOWI4IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:31:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ik9xYjBGZ21jSG5Ia1lYTDR5OC9pQnc9PSIsInZhbHVlIjoiK2g1S2FKMGI1dXpONnBYblZGK3BLanl4cU5FUTNCbmg0ODk0L251eDIzMUpYNW5Ma2l1c3NJWXUwdFY3NzRzOWJzL0w1MS9HQWMwcXVpUkw3V29URkx6RWZIekJ6YkhpNW5JUEJkR1FnSGs5QU5VNEpYbXFkZ3lnVUpDVjdON20ybEdiWXZtd01mVkVPTGdsY2JHcDFPS29uTmNJVXE2VW42V21WTThML0tJUm03WERLeVdtNWxBVGt0N01GelZYR29DNDlLMUE4clprdWwwMGorUTMvM3JwOGJKekdlbkg4S1Fob2xBZWVOa0VTTWs0L0xlNWxvdjIwS21BSS9jOGZDZzBXRlZQcnFBalIyN2RUVEo0WlowVytXa1lwdUVMUmdHbEtOTXluWmFONWJpMGUyZC9INWVQa1pkdWZyYnMxbHVBUzJkcURpNWd3ZFd1SHN3aFlEM1VVZEo3cGY4M1MvVWJLZUhoZjJ1RW1SbVI5VXdDdzZBYlg5eXlUOEVPK0tkOGRlNWFEODNvZElrMUgwV01YUENXNkhhRG4xbm1nNElNNnlVRHBWWTNwdThoMHlia2FoTUQ4V3JTbDdvQ1pGbDc0b3krdFRwVTV4SzRuRU1sejY2QXM3TDl2ZzU2Wm1VYkZQWFBOZGZUL21oRlllMUt2bHBhUXpzeW92K3YiLCJtYWMiOiJjMmM3NzQ1MzViNGJjZjFlMDAyNDJmNGRhMWU5OWIwOTFmZjJjOWRhYzI0ZDg3NTYxYWE5ZjU1YzZhNGFmNjhlIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:31:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InJkVFR6NEVmODVBVGxWSzd3NjdETWc9PSIsInZhbHVlIjoiKzRndjVDeUpyTjdHYUJySk1YQ21mSVkvU1hxVUZ5eDhUamNMTTM4NVpXTTJlL01abkhzZ21HR0FuV2N2OXBDQisrNUdUQk1mVlJ0ek5YVjJLeCtZVTI5WFgzNjN5ZTUvZ0VNTXFoM3ZRYjZyc05xVVY4V0VyelBNNWU3ZXNvR3MwQlNETStGQnUyRnAzc05jbDBzL1FlbjBQbStRay9LWStiWDd2eGVhYnBJb0RvRk5ZWEpnQlRKWUpKcENwN3BqOXowQUFvNFU0czFrNVpLR3FCRkVXRml1Q1gxd0lZSWJRTzJEZmNrMGFjNmdoYmx6dkZlWGhBeDZoY3NmSkdQRnhEWXlNOC9xL0Z5Wk41R0NZWktOTFpIUU9YWEkybFgzQkk4TTlqUDM5NHVqWlBiQ3dZSUovNTZKeG9xWDk0NnRwQU1CRDIxVDBaRGdzMEVPQ1VhVFl1emRQQjcvNnlRRXpnRVRvV3hac0FHUWFkY2Q3djVmOGlLQzlOZjJLdmpaT2ROZGdzcHQ1a3ZIMldFeEJ1d0puZHJmYXpFRlZ3aElqVlYyNE82cXdnUDRXNTEvdGNSUlRmaVpaZ3I1Y1c0SVZCMHM2SUludkRjYlRwdW1aZFVNc1FoNGUyRHQrZWZWc3FTa1JOdVpRYmhKQjhwb0VDV0R2SktONlpLV0dlcWciLCJtYWMiOiI2ZmEyMGI5YjVkNTk3OWM1YjQ2NzUzNTUyY2VhYjc5ZmQ0NTQzMzY0Yzg1ZGE0NjY2Mzc3MDg4N2I0YmFhOWI4IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:31:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ik9xYjBGZ21jSG5Ia1lYTDR5OC9pQnc9PSIsInZhbHVlIjoiK2g1S2FKMGI1dXpONnBYblZGK3BLanl4cU5FUTNCbmg0ODk0L251eDIzMUpYNW5Ma2l1c3NJWXUwdFY3NzRzOWJzL0w1MS9HQWMwcXVpUkw3V29URkx6RWZIekJ6YkhpNW5JUEJkR1FnSGs5QU5VNEpYbXFkZ3lnVUpDVjdON20ybEdiWXZtd01mVkVPTGdsY2JHcDFPS29uTmNJVXE2VW42V21WTThML0tJUm03WERLeVdtNWxBVGt0N01GelZYR29DNDlLMUE4clprdWwwMGorUTMvM3JwOGJKekdlbkg4S1Fob2xBZWVOa0VTTWs0L0xlNWxvdjIwS21BSS9jOGZDZzBXRlZQcnFBalIyN2RUVEo0WlowVytXa1lwdUVMUmdHbEtOTXluWmFONWJpMGUyZC9INWVQa1pkdWZyYnMxbHVBUzJkcURpNWd3ZFd1SHN3aFlEM1VVZEo3cGY4M1MvVWJLZUhoZjJ1RW1SbVI5VXdDdzZBYlg5eXlUOEVPK0tkOGRlNWFEODNvZElrMUgwV01YUENXNkhhRG4xbm1nNElNNnlVRHBWWTNwdThoMHlia2FoTUQ4V3JTbDdvQ1pGbDc0b3krdFRwVTV4SzRuRU1sejY2QXM3TDl2ZzU2Wm1VYkZQWFBOZGZUL21oRlllMUt2bHBhUXpzeW92K3YiLCJtYWMiOiJjMmM3NzQ1MzViNGJjZjFlMDAyNDJmNGRhMWU5OWIwOTFmZjJjOWRhYzI0ZDg3NTYxYWE5ZjU1YzZhNGFmNjhlIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:31:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1538278334\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-761067284 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">UjUQ3NSjsfxYXjXar02iivdJ5YNzIqknuABt5l2G</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-761067284\", {\"maxDepth\":0})</script>\n"}}