{"__meta": {"id": "X1463fd8d6337c87170810ade7f99ffb4", "datetime": "2025-07-29 06:31:06", "utime": **********.829344, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753770665.984191, "end": **********.829398, "duration": 0.8452069759368896, "duration_str": "845ms", "measures": [{"label": "Booting", "start": 1753770665.984191, "relative_start": 0, "end": **********.739066, "relative_end": **********.739066, "duration": 0.7548749446868896, "duration_str": "755ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.739104, "relative_start": 0.****************, "end": **********.829401, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "90.3ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2999\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1805 to 1811\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1805\" onclick=\"\">routes/web.php:1805-1811</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xQiWCvYr4RL3juejIbu9sjsI8SZydx8dxmjqi0e4", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-1425326600 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1425326600\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1321041755 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1321041755\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1713446003 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1713446003\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1331026779 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1331026779\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1333371415 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1333371415\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2033827248 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 06:31:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjFQdUlKUEtPVzFIdTdtdmJtNktYcmc9PSIsInZhbHVlIjoicGNlZk4reW53SDBCcUdqUW43Z3JueCtnMWJsMGp6N3dGd2hzRmhIRTNrMnJmeFFYaHpLaE4rQmpqdi9VTmZyczdyRUNHbU16bWlNS3JvdTFHci9iM3Z6MHlLUEJZRzBoTEt1TzZUNEMyUEVWQUVncnFGb2Z4dzF5MG5UdFduaFoweHJ0bTdTK3hqdEx1NXlsd0RLeTl4bDR5eU9xdUV6dmdsMmo4UkdLYTkwSUdHWnJ4WnBZbWIyR0Zzb0Y4Ukt1THJzODZKdnZzWE1nZGp0c0JKbnhuL1l6NGg1NXVrWEJOVEhXWDA2emxWRWVnZFYzQURsamd6dkRkR2Q1cVVCcmttcFdjalJTcUZPWDc3VTVpWk01NitlaU1lVjdEanJyNkV5WENNdEdmM08vMWNUc3pQYU5WZTZ6cVdXN3ZQWi9NcFZXeE9ySld5K0Z4VGtxNjUzYjR6SytkUFB6dXp5NTFKZHkrSDUyUENqQWpYUlFVNWZCVzZ2SDliM3J0MkJINitrYjNCSmI5NWFwLzVXaGtwSUJhRklCbm05dk9uZlhla1VmTzZINFExZ0MvdHllYS9XMXV3QWR1aVZYTmljVnkyT0R5d3ROTG1xRVpUQStyR2Z0QjIzeTFvaHFONlNhN0x6Y2JRaG1lK2pBV0pKbGt6RXhqc3JPcFFsZE1FUHYiLCJtYWMiOiJiYTMzMjFhZDVkZjhhODY0YzgxYzBlM2UxZTEwNThlZWUxMTFmYzM3MjBkY2RmYzNkOGEyMTJhNWE1NjE1OTQ1IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:31:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InZlRS9RemNNeWtFdDdzclFRZWFKYVE9PSIsInZhbHVlIjoiYUhxUmhIenRqL2xsbUh1Umt6aUpIYmdmM3V2dzh6NDUzT1lCQ1ZzSnpjTVFZWGRyVVFlY21GbnVsOVh0Z0trdEptSll2MWFvQlJzNkloMXA3V2N6ejJwcit1cmtDeUk1alM0V0F1QzJqbThwM1F0STVrT0taT0JwZUFhdTZyZk4yWjNMZmdYRTNUR3pwMlB2Ly9SaDVERmdxM1RRMGlBc2JCYTZkbGNqQWhXMFhHUGV0cFpMclE3dGZkeUlubThIZ1djVlJ4RmwwNzQrQ2lyNlh4c0l2QXUzWSszdzFWbktCamtCTnQxeW9rYk5Vc2JqaGFEQWxEaTBFWmk5UTBDTkJHSVlpd3ZKV0tvRHVLMVdsSmZPbUpaWWJPL2FkdlhGZEVweVprL2VWdC9OOXZIVDVaM0l4R3EzODBLUDBpZjQ0cWFRd25HOFMwRG1vbEJZNlhjc2xESEN0NjBRRDVyWEtLWVM2QUY2QlVjNDczaU5vYnpaMkgxRnUrWG9EelRoekd4eGQ2d0FuZHkwaHRJNjFxNHJNTExUWStsNTZiNGRZZERjNzNJaDhzVmpZdzBtc2lybnQ0REFHT0F3S0V4MVJsL1ZSaStZQWRJSEZLT2NScDQ1RTArUUpUSWhVZGFkUmNmSTAySnJjemd1NmlaOVFIa0FWdnBDMWxaWE9rVEsiLCJtYWMiOiI4ZTVmNzFjMGY2NjY5ZjBhZDdmNDUxOTFiZTI4OTk1ZmEwYzAwNGVhNDkyNzViZjk1OWQ0NzRkMDc3MjkzZmRhIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:31:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjFQdUlKUEtPVzFIdTdtdmJtNktYcmc9PSIsInZhbHVlIjoicGNlZk4reW53SDBCcUdqUW43Z3JueCtnMWJsMGp6N3dGd2hzRmhIRTNrMnJmeFFYaHpLaE4rQmpqdi9VTmZyczdyRUNHbU16bWlNS3JvdTFHci9iM3Z6MHlLUEJZRzBoTEt1TzZUNEMyUEVWQUVncnFGb2Z4dzF5MG5UdFduaFoweHJ0bTdTK3hqdEx1NXlsd0RLeTl4bDR5eU9xdUV6dmdsMmo4UkdLYTkwSUdHWnJ4WnBZbWIyR0Zzb0Y4Ukt1THJzODZKdnZzWE1nZGp0c0JKbnhuL1l6NGg1NXVrWEJOVEhXWDA2emxWRWVnZFYzQURsamd6dkRkR2Q1cVVCcmttcFdjalJTcUZPWDc3VTVpWk01NitlaU1lVjdEanJyNkV5WENNdEdmM08vMWNUc3pQYU5WZTZ6cVdXN3ZQWi9NcFZXeE9ySld5K0Z4VGtxNjUzYjR6SytkUFB6dXp5NTFKZHkrSDUyUENqQWpYUlFVNWZCVzZ2SDliM3J0MkJINitrYjNCSmI5NWFwLzVXaGtwSUJhRklCbm05dk9uZlhla1VmTzZINFExZ0MvdHllYS9XMXV3QWR1aVZYTmljVnkyT0R5d3ROTG1xRVpUQStyR2Z0QjIzeTFvaHFONlNhN0x6Y2JRaG1lK2pBV0pKbGt6RXhqc3JPcFFsZE1FUHYiLCJtYWMiOiJiYTMzMjFhZDVkZjhhODY0YzgxYzBlM2UxZTEwNThlZWUxMTFmYzM3MjBkY2RmYzNkOGEyMTJhNWE1NjE1OTQ1IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:31:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InZlRS9RemNNeWtFdDdzclFRZWFKYVE9PSIsInZhbHVlIjoiYUhxUmhIenRqL2xsbUh1Umt6aUpIYmdmM3V2dzh6NDUzT1lCQ1ZzSnpjTVFZWGRyVVFlY21GbnVsOVh0Z0trdEptSll2MWFvQlJzNkloMXA3V2N6ejJwcit1cmtDeUk1alM0V0F1QzJqbThwM1F0STVrT0taT0JwZUFhdTZyZk4yWjNMZmdYRTNUR3pwMlB2Ly9SaDVERmdxM1RRMGlBc2JCYTZkbGNqQWhXMFhHUGV0cFpMclE3dGZkeUlubThIZ1djVlJ4RmwwNzQrQ2lyNlh4c0l2QXUzWSszdzFWbktCamtCTnQxeW9rYk5Vc2JqaGFEQWxEaTBFWmk5UTBDTkJHSVlpd3ZKV0tvRHVLMVdsSmZPbUpaWWJPL2FkdlhGZEVweVprL2VWdC9OOXZIVDVaM0l4R3EzODBLUDBpZjQ0cWFRd25HOFMwRG1vbEJZNlhjc2xESEN0NjBRRDVyWEtLWVM2QUY2QlVjNDczaU5vYnpaMkgxRnUrWG9EelRoekd4eGQ2d0FuZHkwaHRJNjFxNHJNTExUWStsNTZiNGRZZERjNzNJaDhzVmpZdzBtc2lybnQ0REFHT0F3S0V4MVJsL1ZSaStZQWRJSEZLT2NScDQ1RTArUUpUSWhVZGFkUmNmSTAySnJjemd1NmlaOVFIa0FWdnBDMWxaWE9rVEsiLCJtYWMiOiI4ZTVmNzFjMGY2NjY5ZjBhZDdmNDUxOTFiZTI4OTk1ZmEwYzAwNGVhNDkyNzViZjk1OWQ0NzRkMDc3MjkzZmRhIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:31:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2033827248\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-472364436 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xQiWCvYr4RL3juejIbu9sjsI8SZydx8dxmjqi0e4</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-472364436\", {\"maxDepth\":0})</script>\n"}}