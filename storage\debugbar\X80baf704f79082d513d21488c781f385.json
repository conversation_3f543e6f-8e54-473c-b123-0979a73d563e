{"__meta": {"id": "X80baf704f79082d513d21488c781f385", "datetime": "2025-07-29 06:29:31", "utime": **********.950708, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753770569.218523, "end": **********.950762, "duration": 2.732239007949829, "duration_str": "2.73s", "measures": [{"label": "Booting", "start": 1753770569.218523, "relative_start": 0, "end": **********.624324, "relative_end": **********.624324, "duration": 2.4058010578155518, "duration_str": "2.41s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.624384, "relative_start": 2.****************, "end": **********.950783, "relative_end": 2.09808349609375e-05, "duration": 0.****************, "duration_str": "326ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2999\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1805 to 1811\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1805\" onclick=\"\">routes/web.php:1805-1811</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "23HggoCKcYcVBcfZoY5SaAsMxf3rtCWfgQbfcYlZ", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-1763606892 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1763606892\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1107763383 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1107763383\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1688248029 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1688248029\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-227050175 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-227050175\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-351596640 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-351596640\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-926184188 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 06:29:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Iks0S2FTU2d5enh4R2M0S2F2MHZRUmc9PSIsInZhbHVlIjoiVUUyUzEzWGFSaXJ2WllsNWVBUGhEdFdCK2xGV3g5MVUwOWJERkM5VU5YYkx4enJZYkJhZSt4T0ZXMnNXUUUyaU40MTdvUTFxNmRuTktSTzFzTnBzYnVRajB0eUxtZXZ6MWIwZVFnYUl0UmFxdUcvS3NXYlBNMU96ZTZmbHlVTG00VkpjdytHa0ZoUllrK0RRZ09zVE4yKzcvY2lPdlRJUGlBcHJDUWFCa2xhRVdsNFBNTVBtUHgxcVFNb243ZmdsNmljM0hJWGZZU0FJYW0rTHExdW0weVJQaVBibFZPeTF4dzBHSVluZDdMSkdqQndnS01DVkRRVGV6OGxhbDU1YTNvYW40RHJ3djduUy9IMFR0VVV2T2RUcHFuN3J2RXBYQk54ekVTU1RxUmF2ODVKNjRKMUozUUt1SjRJaGVnOWRRVTJBRHp1UldwR2dLbUFHWEl5Z1phamlQSm9BT2YyQXlndy9hQTJOZEtHYU5TdFJwNW5Lb24yNUJMMUZMQXE3TlhjREdqeTdka3pPMlZIeWhidWFBWGRaZG9mNW5aNGN4VkN0enpiVTRRbGlhU2YzTXkrTzAwMkZ3ZFhKamMwVXN2VkJ5NEpMRlk3NVVDODZ0cDRFVjJESkt4L05yOEllVlZ0WEU1RG43ZnpzeWhtdDk4V29FRnN3OWswU1ZYd3QiLCJtYWMiOiJmMDM2MDczZGU5NThiOWMxNzI2Yjc5MzRkMjkzZDQ3ZThjNzI2YjQzYzQ2NTg5N2M0NWZiZTFjYWYzZTQ4YzRjIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:29:31 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ikg3Z2VneUExL0kvdzZ0aDM4QVA1Unc9PSIsInZhbHVlIjoiTDl0VU05bFI4QzhhdmNyb2J4VDdOVE5OZWQyWDJEWDhoSGIxTHMrTlJ4bTBoRHg0LzJ5TGdFbGVOWWF5UVlaYnVOK3Nhb21qQ3EyVGFueVU1SlovVFA5aFJtUXNVMDZYdVJFMG5UOEc3bVhNNWdFR0EvMk9yVXZySmNVNW4vSWZDazhXY0VmOWRLZ0h6UXJNTUtxMzk0SDlvUUh5R0duU3Zjd3JWcXZETEtmZXdSQ2htUUtROHcwcC8reStlR1ZKdUZhWU56R2pLRTNkRGZkNks4eTk2SDBxejdRMGdNOXUvb1poMytNOUlPb1hycXdxU0Q3cjhIL256cFRJZUhBdDh0aEhQOXYwbndDVnN3dFV1d1VLYUFIRnJNUzdFSk1UUHJpaHJiaTNQYmwxeHJ1OEsxQW1xdldFbHozQnZFVzhGczhHODh3d0pXeFpNbUFjOVczSVpLRnNPbkszamlmRFU0emhmclZHZGEyYjUxeU1UeTNnL2hSVzVjUHhWMnVCR25UK29YRWVNc0N1eGxYU2xXa1lpdHVURU1XeGlqa2taTUdxUlhtbkh6WlJLeXhZVjJHKzFrQlhzREZ4SENsQjF6YWZROTdqYXdoK1ZHNFRrM0E2WFhYYkxCR0tCcms0QnRlMHdQQ05Nc0poM2M3QWFmc1ZPWmsyUnFLbWtVTFoiLCJtYWMiOiIxY2FmNTA2ODA3ZDk2NjY1NzE3YTM1ODVjNWFhN2U2NmVmYTk2MjhlZTdhMmQ1YTc0MmYzZWE4MTM1MWEwMWM3IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:29:31 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Iks0S2FTU2d5enh4R2M0S2F2MHZRUmc9PSIsInZhbHVlIjoiVUUyUzEzWGFSaXJ2WllsNWVBUGhEdFdCK2xGV3g5MVUwOWJERkM5VU5YYkx4enJZYkJhZSt4T0ZXMnNXUUUyaU40MTdvUTFxNmRuTktSTzFzTnBzYnVRajB0eUxtZXZ6MWIwZVFnYUl0UmFxdUcvS3NXYlBNMU96ZTZmbHlVTG00VkpjdytHa0ZoUllrK0RRZ09zVE4yKzcvY2lPdlRJUGlBcHJDUWFCa2xhRVdsNFBNTVBtUHgxcVFNb243ZmdsNmljM0hJWGZZU0FJYW0rTHExdW0weVJQaVBibFZPeTF4dzBHSVluZDdMSkdqQndnS01DVkRRVGV6OGxhbDU1YTNvYW40RHJ3djduUy9IMFR0VVV2T2RUcHFuN3J2RXBYQk54ekVTU1RxUmF2ODVKNjRKMUozUUt1SjRJaGVnOWRRVTJBRHp1UldwR2dLbUFHWEl5Z1phamlQSm9BT2YyQXlndy9hQTJOZEtHYU5TdFJwNW5Lb24yNUJMMUZMQXE3TlhjREdqeTdka3pPMlZIeWhidWFBWGRaZG9mNW5aNGN4VkN0enpiVTRRbGlhU2YzTXkrTzAwMkZ3ZFhKamMwVXN2VkJ5NEpMRlk3NVVDODZ0cDRFVjJESkt4L05yOEllVlZ0WEU1RG43ZnpzeWhtdDk4V29FRnN3OWswU1ZYd3QiLCJtYWMiOiJmMDM2MDczZGU5NThiOWMxNzI2Yjc5MzRkMjkzZDQ3ZThjNzI2YjQzYzQ2NTg5N2M0NWZiZTFjYWYzZTQ4YzRjIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:29:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ikg3Z2VneUExL0kvdzZ0aDM4QVA1Unc9PSIsInZhbHVlIjoiTDl0VU05bFI4QzhhdmNyb2J4VDdOVE5OZWQyWDJEWDhoSGIxTHMrTlJ4bTBoRHg0LzJ5TGdFbGVOWWF5UVlaYnVOK3Nhb21qQ3EyVGFueVU1SlovVFA5aFJtUXNVMDZYdVJFMG5UOEc3bVhNNWdFR0EvMk9yVXZySmNVNW4vSWZDazhXY0VmOWRLZ0h6UXJNTUtxMzk0SDlvUUh5R0duU3Zjd3JWcXZETEtmZXdSQ2htUUtROHcwcC8reStlR1ZKdUZhWU56R2pLRTNkRGZkNks4eTk2SDBxejdRMGdNOXUvb1poMytNOUlPb1hycXdxU0Q3cjhIL256cFRJZUhBdDh0aEhQOXYwbndDVnN3dFV1d1VLYUFIRnJNUzdFSk1UUHJpaHJiaTNQYmwxeHJ1OEsxQW1xdldFbHozQnZFVzhGczhHODh3d0pXeFpNbUFjOVczSVpLRnNPbkszamlmRFU0emhmclZHZGEyYjUxeU1UeTNnL2hSVzVjUHhWMnVCR25UK29YRWVNc0N1eGxYU2xXa1lpdHVURU1XeGlqa2taTUdxUlhtbkh6WlJLeXhZVjJHKzFrQlhzREZ4SENsQjF6YWZROTdqYXdoK1ZHNFRrM0E2WFhYYkxCR0tCcms0QnRlMHdQQ05Nc0poM2M3QWFmc1ZPWmsyUnFLbWtVTFoiLCJtYWMiOiIxY2FmNTA2ODA3ZDk2NjY1NzE3YTM1ODVjNWFhN2U2NmVmYTk2MjhlZTdhMmQ1YTc0MmYzZWE4MTM1MWEwMWM3IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:29:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-926184188\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-249850830 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">23HggoCKcYcVBcfZoY5SaAsMxf3rtCWfgQbfcYlZ</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-249850830\", {\"maxDepth\":0})</script>\n"}}