{"__meta": {"id": "Xb9cf40dcc2e8dafcd7ff6d7d832b3f31", "datetime": "2025-07-29 06:30:54", "utime": **********.963126, "method": "GET", "uri": "/api/leads/19", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.038829, "end": **********.963149, "duration": 0.9243199825286865, "duration_str": "924ms", "measures": [{"label": "Booting", "start": **********.038829, "relative_start": 0, "end": **********.854936, "relative_end": **********.854936, "duration": 0.8161067962646484, "duration_str": "816ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.854963, "relative_start": 0.816133975982666, "end": **********.963151, "relative_end": 1.9073486328125e-06, "duration": 0.10818791389465332, "duration_str": "108ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46077048, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/leads/{id}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ContactController@getLeadForPreview", "namespace": null, "prefix": "", "where": [], "as": "api.leads.show", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=531\" onclick=\"\">app/Http/Controllers/ContactController.php:531-567</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.01153, "accumulated_duration_str": "11.53ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.907042, "duration": 0.0075, "duration_str": "7.5ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 0, "width_percent": 65.048}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.927027, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 65.048, "width_percent": 15.785}, {"sql": "select * from `leads` where `id` = '19' and `created_by` = 84 limit 1", "type": "query", "params": [], "bindings": ["19", "84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 537}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9334261, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:537", "source": "app/Http/Controllers/ContactController.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=537", "ajax": false, "filename": "ContactController.php", "line": "537"}, "connection": "omx_sass_systam_db", "start_percent": 80.833, "width_percent": 7.025}, {"sql": "select * from `lead_stages` where `lead_stages`.`id` in (118)", "type": "query", "params": [], "bindings": ["118"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 537}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.940935, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:537", "source": "app/Http/Controllers/ContactController.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=537", "ajax": false, "filename": "ContactController.php", "line": "537"}, "connection": "omx_sass_systam_db", "start_percent": 87.858, "width_percent": 6.245}, {"sql": "select * from `pipelines` where `pipelines`.`id` in (30)", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 537}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.946774, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:537", "source": "app/Http/Controllers/ContactController.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=537", "ajax": false, "filename": "ContactController.php", "line": "537"}, "connection": "omx_sass_systam_db", "start_percent": 94.102, "width_percent": 5.898}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}, "App\\Models\\LeadStage": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLeadStage.php&line=1", "ajax": false, "filename": "LeadStage.php", "line": "?"}}, "App\\Models\\Pipeline": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPipeline.php&line=1", "ajax": false, "filename": "Pipeline.php", "line": "?"}}}, "count": 4, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/contacts\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/api/leads/19", "status_code": "<pre class=sf-dump id=sf-dump-2069370344 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2069370344\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1241991649 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1241991649\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1665057538 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1665057538\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-281726788 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IjdHekN0ajVFQjMyNWJvYnVZZWM0akE9PSIsInZhbHVlIjoieGRleUxQOG1MOGMzNHUwR2puL0IxZ3h4Z3FxT3hCZ1V5bmRkd241NHRXNS84ajNxalRQa2daQUovYWZLRGUvYmxaN29DQ1NTc1pKZTNVa0d2SHdyU2ErMzhleVdScmdzMllQVUJXZTdOREhQYzdVM3hybTRKaitqSWNCc2pib1NZWVlCQ3BTQ2VMTVVwd2F0WmU3SjdHWUlNWTRtQXl4MFMxZzdzUHBKU3dKcFpOTCszS0FMU3BuamtiWmR5dFZjNzlNaWdzd3d1S1R3VUdHaUxpYVBrVURTZjVpU1Y2YWZCbjdsT25mcDJOTXFaZjJEcEZnajlJQUpvK09MQkFVOWEwSHRQSzRPTEw1QW1LbTJQWEEvS2RBWWgxS0xxTU15cjBEcTNvb2dyNndPY2hWOHhCd1hJdG9keXYzU2NLSnNlclZQVTEzMTR5V3JyUmxaNFMxcTlrZE93dWpDN0o5QWxaNlV3Ri84TEFiNWtqbldsZWlDQ2pZS2djMVJ6QTFibUtNdzErVENLMHRxVEJoRDI4QkJlcHE1dU5FNk5SWXhqdjZhVWE5amlwM3ZSVDAxamFBb1AyWHJEd001UHJSem1RTGhHZ2tLTXFSVXV5dVN1cmtlSWltT2NuOFljckxlVFZkTnhlTjRSeXZpYUovQUt5N1YwUW5pcW12eUZSMlciLCJtYWMiOiI4NTA4MzMwOTdkNzAxNTZlNjMyZWMwYzZjYmE2NmNjMzdmNWIxOTE0NjQxNTVhYWY1NDg4OWM0ZGU3ZDg0MDdlIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlovdFZOSWtyRlI5Y3dsaFJQdFdkTnc9PSIsInZhbHVlIjoiUGw4OW9uQ0pqL2NrTFplN3hPeGhjUnFiQXUwVGZpYW1IaGtTNTBHaVBkWDc2OW5pTXBFN0RqdStpb2RVOElFTW1DSlA5UXNNa0E3Wkt4VTFQSG0wME5wcWZVZm00YzF2WVRvMDY0ZjRobkNkTzRocllOcEZ1SGVMQVNCZ3ArRWdVMldLVThIcGdvMXZBakFrRUN1SCtIbTZZTUQrb2ozRjFXMUJySkwvY1RyMVExRkxMeU1SZndZWmdtd2NkOTcwTGEyOXhwUFF2TXJhdXJNemdiTVh3ZE05YVZSZ2RVWGRnK1VJRXFqcWljTG8rMlQ0TVhOMWptVkJXbGkvMHJ0NGpIV0t2Z3VLdmxFemRKa0I0ZnJMNThYWVdlNWtuQlNzL3V2ZTd3clNOMU1PcWhlY0pJd0Z4QzhtbDN5YlByRUpjb0oydE5xWFNWeVYycnMrMTZTU1RBeVBqWCtZTzlqR1dDd0pwUHhhOFpFanA2YzZnQ0JXV1hyZW9YSDd2K3AxeCt4bEZOZWQ3VGtsbFF6eTU2QjJVM2ZtTDNnWHNlY1FYemt4TGhUZDZJamRVVVBhcFVjR2RvMEQ1YVpIc3hUZkNWK0N0b3JPMS9FdmxVbEpmU3lkNC9IYVFsYmoxa1lna2U1TmNraWRWNnR5T2hRTHFYYUdsR3h3cjNLR0JYanEiLCJtYWMiOiIyNzVhNWJhOWVlNjNiN2E3NGU1YWVhMWZhOTExMThmNzExMGVjNjk4NzJkMDE1YWU0ZDhjMjBkN2I4MWM2NGMwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-281726788\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2120866236 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1XDrxTWmYv3gorVrXvmSodhuVHNiE4Wue14QMI4v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2120866236\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-672905426 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 06:30:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InJKTlNGeTRNOWZMNUtJOHJzZW02NFE9PSIsInZhbHVlIjoidytJUEVuWTFWbCtMY1VoNkwxQ0tRa1ovWENaUUJsYXF1VHExRmJLK1BpQ3BrOFJBUVhUZ0hZQzdiOGphUVJkNXJhNytuWmFVQzZ3Zm5ra2ppY240MVJWN1VOdXo0TzIyY1lKNXZWMjBMa24xazlnWnpoNHovaUd4OExpTFlKZjlRQ3Vnc2tjaEdPSjV3SGYwU1EvamVCalFlcnRIWm1vZW41YmVPUnhDK0VKdm1GN20xWDdzMTdCdmhFemxDWkZOY0RsaDJzU1JqN3RqeFZFVnhHOTc2K2hCNU41SE80eW9IZ1IvQlBXK2VJekZ1MVBzcWl4OHVSaWpUeEhVTnBpbzRma1hmNVh1K1NQZTRqdXJqNnFXN0Q5U3JZaTFJZ0YwaHJSU0VnT2tTc3NBRGV1NVJGUi9Pekk2WVY0RXZKMDkxR2poQmJPdDEwcU92cndEWStLTE9aNWN2NnhyR1VTOGhhWlJnbFZNOHVnMmt4QitISnlLdlk2bnlZeTR4OHdIYUpCMmJOQlY4UmhPWjdYUktDbTJNUDZmVjBYLzRhQ0MzelVoM3dqbXJ5MGtOSThHK3FuamN1ODFtR3B0aUh6ZVpwT2ZKQzVTc29QZ0dQTFA0ZEk5VGt3MTZmbGE4MjdLTGM2UW5pS2lhNUo1LzhERFdpTzFjQVVIZVAyaC9XL1QiLCJtYWMiOiIxN2RkMzBmNjU0YjNkOTczYzg0OTNhMWE4N2MwMmE4M2MwMTQ2NTY0NTczZjQ0MDJlMGY0MmQzNzhmMDQ0ZGZiIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:30:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImY1dUs5TitkVUd6U0s4MlNySnRnRlE9PSIsInZhbHVlIjoiYk94WlgzNDA3WlAvQU5qUVF6NmhaeGo0eTdzUUJvc3puQlpRVVVBNGJHcTVnNjYyQUR0Q29ORGxYa3hjbU9ncHYzU3dWaDU5clpMdlBGclFyY1hLRERYZS9WdWNXY1dycThOT0I0c0RIdy81cENqLzhTZSt2ak9leVkrL29VWnJxaG9BNzRaeXVEVDkvekpPY0t4bmdENWZRL1dJZjl6dE9McUtuaXVVWmRGbkh2MUJscVp2VFp3eFJwbDBjeHI0ZkxUZ1Z0UmpDVTJIY2FCUmdRMUtiNkthOW5JOG9CdXRBd2FvTG44S3kvb3Y1azJVcVUxck1ObVJPRGEyOTdNYko3VWl1MXFJZ0ErNEQreGlBWnBKVkluNVI0a1JxRGhOdWJTTXNmTVRXalJ1VkFyMU4xb05OcXVhQ3AveVA0TEs2QVR5R1Y3eDE5RDdKMms3YjB0WlVzbVNheFd5d1p4UVYxWTFlMTlvRVZvSzNJek03bXgxTUV2M0dxYm4za1dEb3VQQmV2Uy9WeWpzbHdDcWhMN1FIV3NtR1JleDEvdXRpZ1A5WURBMnRIRDZWdnRvSnlSOHNnSTczZjNyQktsSzFOcFUzRTRhUXIvSHM4bDN5ZnZwNTMzSDBidUR2cURjRXFNZzhPUXU5OHlybkRlQWdXSVVtMGhpaVRQRlAyTGciLCJtYWMiOiJmMzUyMGFkYzUxY2RkYzU5MGI2NzNhZjU1N2Y0NjYyZmUwYjE1OWE4NTJhNjgzMGQ3NmFjNGU2OGQyZjAxMTk1IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:30:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InJKTlNGeTRNOWZMNUtJOHJzZW02NFE9PSIsInZhbHVlIjoidytJUEVuWTFWbCtMY1VoNkwxQ0tRa1ovWENaUUJsYXF1VHExRmJLK1BpQ3BrOFJBUVhUZ0hZQzdiOGphUVJkNXJhNytuWmFVQzZ3Zm5ra2ppY240MVJWN1VOdXo0TzIyY1lKNXZWMjBMa24xazlnWnpoNHovaUd4OExpTFlKZjlRQ3Vnc2tjaEdPSjV3SGYwU1EvamVCalFlcnRIWm1vZW41YmVPUnhDK0VKdm1GN20xWDdzMTdCdmhFemxDWkZOY0RsaDJzU1JqN3RqeFZFVnhHOTc2K2hCNU41SE80eW9IZ1IvQlBXK2VJekZ1MVBzcWl4OHVSaWpUeEhVTnBpbzRma1hmNVh1K1NQZTRqdXJqNnFXN0Q5U3JZaTFJZ0YwaHJSU0VnT2tTc3NBRGV1NVJGUi9Pekk2WVY0RXZKMDkxR2poQmJPdDEwcU92cndEWStLTE9aNWN2NnhyR1VTOGhhWlJnbFZNOHVnMmt4QitISnlLdlk2bnlZeTR4OHdIYUpCMmJOQlY4UmhPWjdYUktDbTJNUDZmVjBYLzRhQ0MzelVoM3dqbXJ5MGtOSThHK3FuamN1ODFtR3B0aUh6ZVpwT2ZKQzVTc29QZ0dQTFA0ZEk5VGt3MTZmbGE4MjdLTGM2UW5pS2lhNUo1LzhERFdpTzFjQVVIZVAyaC9XL1QiLCJtYWMiOiIxN2RkMzBmNjU0YjNkOTczYzg0OTNhMWE4N2MwMmE4M2MwMTQ2NTY0NTczZjQ0MDJlMGY0MmQzNzhmMDQ0ZGZiIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:30:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImY1dUs5TitkVUd6U0s4MlNySnRnRlE9PSIsInZhbHVlIjoiYk94WlgzNDA3WlAvQU5qUVF6NmhaeGo0eTdzUUJvc3puQlpRVVVBNGJHcTVnNjYyQUR0Q29ORGxYa3hjbU9ncHYzU3dWaDU5clpMdlBGclFyY1hLRERYZS9WdWNXY1dycThOT0I0c0RIdy81cENqLzhTZSt2ak9leVkrL29VWnJxaG9BNzRaeXVEVDkvekpPY0t4bmdENWZRL1dJZjl6dE9McUtuaXVVWmRGbkh2MUJscVp2VFp3eFJwbDBjeHI0ZkxUZ1Z0UmpDVTJIY2FCUmdRMUtiNkthOW5JOG9CdXRBd2FvTG44S3kvb3Y1azJVcVUxck1ObVJPRGEyOTdNYko3VWl1MXFJZ0ErNEQreGlBWnBKVkluNVI0a1JxRGhOdWJTTXNmTVRXalJ1VkFyMU4xb05OcXVhQ3AveVA0TEs2QVR5R1Y3eDE5RDdKMms3YjB0WlVzbVNheFd5d1p4UVYxWTFlMTlvRVZvSzNJek03bXgxTUV2M0dxYm4za1dEb3VQQmV2Uy9WeWpzbHdDcWhMN1FIV3NtR1JleDEvdXRpZ1A5WURBMnRIRDZWdnRvSnlSOHNnSTczZjNyQktsSzFOcFUzRTRhUXIvSHM4bDN5ZnZwNTMzSDBidUR2cURjRXFNZzhPUXU5OHlybkRlQWdXSVVtMGhpaVRQRlAyTGciLCJtYWMiOiJmMzUyMGFkYzUxY2RkYzU5MGI2NzNhZjU1N2Y0NjYyZmUwYjE1OWE4NTJhNjgzMGQ3NmFjNGU2OGQyZjAxMTk1IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:30:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-672905426\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1488719667 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1488719667\", {\"maxDepth\":0})</script>\n"}}