{"__meta": {"id": "Xa8680242b8f1ed58262c99a91ee7e1dd", "datetime": "2025-07-29 07:09:00", "utime": **********.245918, "method": "GET", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753772939.235587, "end": **********.245943, "duration": 1.0103561878204346, "duration_str": "1.01s", "measures": [{"label": "Booting", "start": 1753772939.235587, "relative_start": 0, "end": **********.162635, "relative_end": **********.162635, "duration": 0.9270482063293457, "duration_str": "927ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.16265, "relative_start": 0.9270632266998291, "end": **********.245946, "relative_end": 2.86102294921875e-06, "duration": 0.08329582214355469, "duration_str": "83.3ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44199184, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=263\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:263-278</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.00383, "accumulated_duration_str": "3.83ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "guest", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\RedirectIfAuthenticated.php", "line": 25}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2167192, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1104577933 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1104577933\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1201961005 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1201961005\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-707487879 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6InZMcGhZNTRTK1plT3FLd0p0ZTBqdUE9PSIsInZhbHVlIjoiTE92QWRaVlRpSGNpbU8zWStqNDR5Q1RYTlIxeXgxMmcrNGdmZnBqWHJ5L3FNNG8yUXcwM1M2MzlMMlM0ZWVBVW55aGNzMEZDNWV0S3d2Y0lxOGgwUTVBak9TVUw1NmV6N3Q5MUFxK0ZlZ0dRd0VDaFdSUGdQdlJienNoZ0hRT1ZlcWxOb3RQZTVWQnRPektqdzJVOWNBcmcxVFhJKy92UG9zMVVxZSs3U1AwYjNxcSs0S0Z0dHZSaGcydUZMQUFUcFRBV3I5WTZ0R1VSV2ROL0h0aHErWkxGanBhZ2pvTmVOWVZ1aFhWSHN6SUk2ZE5Id21lY2QwMzh2ckhKOWJNU1k0b3FpYlJ5Yk9iMnpwaEo0U2lpdSs2ZlI0VGs1Z2FmK3ZGUDlRS2FMdXFjRGNvbDBtaUVjblpUUll2MlNlY2xISjZmVGdaQ2ZBeW9yQlEvR1hsOXBVWTU3WWhXVGxoV0RzOEhKZzRXVFVNUWtoMEROWlpsblJZMU1BeWx3VGpUZ3lLaHNwMHViVVZ3ZGV1b1FpcTZpcWhLSHJIU3ZlbHNGVEFsczFGYlZPby9yYklRRXY1dmVKK3E1ZEJVN0NPZkFlWGlVQjM4WGcyM3dNWmNna3VBQnhCWUx1dTNIbkhmc1dvTmhYelFLT1E2clQvZzNGbGM3MDlCRVFBTG5YNmEiLCJtYWMiOiJlMDU1MGJmNGEwMDUxZGUyNGNlMmU5NDNmZGIxOWJkYmM1ODExN2I0YTE3Y2Q0MmVjODI4MTRlMGZmMjJiNjQyIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlBhd1V4R2l3bVJiN2xyaktOWnRaM2c9PSIsInZhbHVlIjoiUGJycWNZTDA4ek5MSUUwZzQ0akEzZEdTWmpwK0JyVlh1enhURUtKYzNUY0czRjMvRW0vWldPUkNNVnptazR4c3ZobGJvbnBkWXRWdmtoNEdCMDZqTXZuMjRJNzFHb1YzREgvckRtWUZ1TWFEWWRUSTBrcUl3azRBQ20rd0QzckkweFRncjI5eFBZVVRUOGpBd2Q0dE5BUWpad0Fqem5ZNmM0UTFYc21VR21ubHI1K3VqMndiTTBqdVBQNXlUWWhoa2tqNm5UMDFWaktDYWJQUy9JeHBVbm5xK3ViZU9JQ210U1BIVFJYeEZXY1M3RHRvckplMmRWTDl2dGV5ZXA2RzhXRTArM0tjcnNpYVZBWGFVV3NVaTZlS1FlNzN2aVk2M0JHd203QVJiTkVISXFNU1cxbDZ6VmlyVnhlT1dxUFYyWElyVnpqR0wyVUpRZWtWWnNuVjdZUXA2Q2g5d2Y3b2srbFZ2MkgwcEoyazduK3FzMFhvdDRra0p4VmpJVWVsWVVTTVErcE5KZ2cvN1hhU1BKVVpLNWplbnNhWTBqVk5MNGhjRC9seEZQbzE2Y1BOdUg4QzI1SFA1bGdaNExCTEdDU3FOR2xoT2VzMFEwTUNGNDhzdDFCYUJmTFhnS0V2N2FTQ2NGNG0rS2J5SkludnRwQUNHTENLR1dNUSsvd2giLCJtYWMiOiIzODFlOWZmNDQwNDQxNTMzNjQ0MzYyNTE0OWMxNmVjMWI1M2Y5NTcxYjU0ZTkzYzBhNjhiZThmYzg5YzlkNDhhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-707487879\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-227970777 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1XDrxTWmYv3gorVrXvmSodhuVHNiE4Wue14QMI4v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-227970777\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1025386262 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 07:09:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImhzME85b29TcSthYjBlRllBdkNDdmc9PSIsInZhbHVlIjoic2JQSklXeVBmZjVTMUFnTC9FbzRaUUxXYjJ0YWI0Q0VOMi82R2d3QjZaamd4TE9MZExTUzUrTFF3SnpaNnkyeWRuSXBUci8xQ3l4WnFRUjllSytoRlVKd3hhbVhFM0hhWU82TVFYdy9EeDVTUkwyNFMxZkdIVlEzNzRGTWpyd0ZXWEowdzA5aSt0MnJXTll4SnF3QXZjWjdraDZBcmF2dTFld1MzTUFsUGVLamhwTXBvMDhlZVBpaDNFcDJGZGtKdHpjYURxaG5uTG15TWRZTnB6TjhabjQ1dU9pVkdmdFFOdVFTenVlSjgrdmtsdFIrRVFaVTZtR1VkQXhDQk44anpkR09zYnFWcEZDLzNJRFVKWkNieVZaZVM5M05NM0x6TklUdW5OMHU1ZHNKMDdRbGxPbzRVeEhyQ09XalpJWWNwZEd1bHBnYjVGbkptZDRUd3VHTmFqTlpPNzU2ejIyeHk1NWZNQmlVeU1GNFpuQ0xQMnZ2eDhSYWVPM0xOSUc2cXVDeWJtaDNGQjNZenN2QTdXWEJlTlE2L1Q2dVc3by9CS1Y1a2VCNXNLUjdhays2bUxLdWtkdXI1SGR2dHowQjQ0VDhMZXRaSE55dkNlSEdRK3VZM05nQVhKbFhiSG02cWJLRjJDNTBXSlQvNWNwVktHNDdtcG0wMkJUZTRyd3giLCJtYWMiOiJiZDY4NDJmZTk5NzQwNmE3ZDAzZjlkMTJhNWZjMmYxYWM5NTMwNTc3ZDM5ZmRmOTI2ZjMwOTExNmYxYTBmMDQ3IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 09:09:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Inh0WlNBQnZsYzVmdDZCTTR1WGxMK3c9PSIsInZhbHVlIjoiQ1Fibk5ycmNRMjhNZmsyK2UyNGVIWW5ndUJrRWFwTWpQQTdwQmtybWowaFRLY00rK2dWTW9CUE1hajVMamRHcFBkUFlaa1QrRUozNzFuOXU5TFk5WVlvOEt4TXJ5bzVWSmVFRXU1Q09ZZnlQdEp4VFV5WUd4OTdVTmNKbjZ3cnBkcWt1K1dPNTZ3ekxHb2FnOXFIY09BaWFuWFc5TXFndmxIK0ljZXgxSE5OVC9saHJOZUtHLzlvQm1RK0x4eTVIdmJpNTVEdDJUVFlwL2czWURaeUMwYytzbHVpOHBaMEFjdnk5SFBqMi9GRkFvZWthL2NXZE95S0JqS0FSWHlwdmgwc1oyS0tRQTE2eGJXNHNuOGlUTmJwTFlzOXBZUlBjaTFEdEZIVFpoQVRRT2oxcG02c01WR29BakM4bUx2cEswbEFkWUI1V2dNclhGNWRYZE9NVHNvT0hhNE1OWVZlYjRtVGkvZ29jV2NsUmlybXRXby9OOVdtanhjSFp6ZEsweXU5RVVSdHdjRW0yNll3d1dhZVlHT0RGWHJ0WVlNVEtzZXVKdGZkdnZ3NzA3blRmYUE3VVBJMXJwQmh6QTg0cU5WS2l2K1JDUzQ2OTkxR3p3VExHdmZlNHg2NEpaSkw0ai9zV0NDMzJ2RlI0M3ZJckUwSEE4Qi8zeUJnZTdwVXUiLCJtYWMiOiI4YTUzNGMzMTE4NWU4ZWZhYmVmYjIxMDg3MzVkODA4YWU4YTJkN2M1M2FkYTcxNWZhNmExNzRmZjhjYTBjMTg5IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 09:09:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImhzME85b29TcSthYjBlRllBdkNDdmc9PSIsInZhbHVlIjoic2JQSklXeVBmZjVTMUFnTC9FbzRaUUxXYjJ0YWI0Q0VOMi82R2d3QjZaamd4TE9MZExTUzUrTFF3SnpaNnkyeWRuSXBUci8xQ3l4WnFRUjllSytoRlVKd3hhbVhFM0hhWU82TVFYdy9EeDVTUkwyNFMxZkdIVlEzNzRGTWpyd0ZXWEowdzA5aSt0MnJXTll4SnF3QXZjWjdraDZBcmF2dTFld1MzTUFsUGVLamhwTXBvMDhlZVBpaDNFcDJGZGtKdHpjYURxaG5uTG15TWRZTnB6TjhabjQ1dU9pVkdmdFFOdVFTenVlSjgrdmtsdFIrRVFaVTZtR1VkQXhDQk44anpkR09zYnFWcEZDLzNJRFVKWkNieVZaZVM5M05NM0x6TklUdW5OMHU1ZHNKMDdRbGxPbzRVeEhyQ09XalpJWWNwZEd1bHBnYjVGbkptZDRUd3VHTmFqTlpPNzU2ejIyeHk1NWZNQmlVeU1GNFpuQ0xQMnZ2eDhSYWVPM0xOSUc2cXVDeWJtaDNGQjNZenN2QTdXWEJlTlE2L1Q2dVc3by9CS1Y1a2VCNXNLUjdhays2bUxLdWtkdXI1SGR2dHowQjQ0VDhMZXRaSE55dkNlSEdRK3VZM05nQVhKbFhiSG02cWJLRjJDNTBXSlQvNWNwVktHNDdtcG0wMkJUZTRyd3giLCJtYWMiOiJiZDY4NDJmZTk5NzQwNmE3ZDAzZjlkMTJhNWZjMmYxYWM5NTMwNTc3ZDM5ZmRmOTI2ZjMwOTExNmYxYTBmMDQ3IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 09:09:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Inh0WlNBQnZsYzVmdDZCTTR1WGxMK3c9PSIsInZhbHVlIjoiQ1Fibk5ycmNRMjhNZmsyK2UyNGVIWW5ndUJrRWFwTWpQQTdwQmtybWowaFRLY00rK2dWTW9CUE1hajVMamRHcFBkUFlaa1QrRUozNzFuOXU5TFk5WVlvOEt4TXJ5bzVWSmVFRXU1Q09ZZnlQdEp4VFV5WUd4OTdVTmNKbjZ3cnBkcWt1K1dPNTZ3ekxHb2FnOXFIY09BaWFuWFc5TXFndmxIK0ljZXgxSE5OVC9saHJOZUtHLzlvQm1RK0x4eTVIdmJpNTVEdDJUVFlwL2czWURaeUMwYytzbHVpOHBaMEFjdnk5SFBqMi9GRkFvZWthL2NXZE95S0JqS0FSWHlwdmgwc1oyS0tRQTE2eGJXNHNuOGlUTmJwTFlzOXBZUlBjaTFEdEZIVFpoQVRRT2oxcG02c01WR29BakM4bUx2cEswbEFkWUI1V2dNclhGNWRYZE9NVHNvT0hhNE1OWVZlYjRtVGkvZ29jV2NsUmlybXRXby9OOVdtanhjSFp6ZEsweXU5RVVSdHdjRW0yNll3d1dhZVlHT0RGWHJ0WVlNVEtzZXVKdGZkdnZ3NzA3blRmYUE3VVBJMXJwQmh6QTg0cU5WS2l2K1JDUzQ2OTkxR3p3VExHdmZlNHg2NEpaSkw0ai9zV0NDMzJ2RlI0M3ZJckUwSEE4Qi8zeUJnZTdwVXUiLCJtYWMiOiI4YTUzNGMzMTE4NWU4ZWZhYmVmYjIxMDg3MzVkODA4YWU4YTJkN2M1M2FkYTcxNWZhNmExNzRmZjhjYTBjMTg5IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 09:09:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1025386262\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1807504155 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1807504155\", {\"maxDepth\":0})</script>\n"}}