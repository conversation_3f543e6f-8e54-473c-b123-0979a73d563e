{"__meta": {"id": "X4f2e6a3b17304154e365259df0aeff39", "datetime": "2025-07-29 06:37:48", "utime": **********.884167, "method": "GET", "uri": "/api/leads/21", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753771067.588999, "end": **********.884245, "duration": 1.295245885848999, "duration_str": "1.3s", "measures": [{"label": "Booting", "start": 1753771067.588999, "relative_start": 0, "end": **********.709919, "relative_end": **********.709919, "duration": 1.120919942855835, "duration_str": "1.12s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.709934, "relative_start": 1.1209349632263184, "end": **********.884247, "relative_end": 2.1457672119140625e-06, "duration": 0.17431306838989258, "duration_str": "174ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46081216, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/leads/{id}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ContactController@getLeadForPreview", "namespace": null, "prefix": "", "where": [], "as": "api.leads.show", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=531\" onclick=\"\">app/Http/Controllers/ContactController.php:531-567</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.0095, "accumulated_duration_str": "9.5ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.800438, "duration": 0.0056500000000000005, "duration_str": "5.65ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 0, "width_percent": 59.474}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.835531, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 59.474, "width_percent": 15.789}, {"sql": "select * from `leads` where `id` = '21' and `created_by` = 84 limit 1", "type": "query", "params": [], "bindings": ["21", "84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 537}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8422291, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:537", "source": "app/Http/Controllers/ContactController.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=537", "ajax": false, "filename": "ContactController.php", "line": "537"}, "connection": "omx_sass_systam_db", "start_percent": 75.263, "width_percent": 9.158}, {"sql": "select * from `lead_stages` where `lead_stages`.`id` in (118)", "type": "query", "params": [], "bindings": ["118"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 537}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.856653, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:537", "source": "app/Http/Controllers/ContactController.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=537", "ajax": false, "filename": "ContactController.php", "line": "537"}, "connection": "omx_sass_systam_db", "start_percent": 84.421, "width_percent": 8.105}, {"sql": "select * from `pipelines` where `pipelines`.`id` in (30)", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 537}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.860588, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:537", "source": "app/Http/Controllers/ContactController.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=537", "ajax": false, "filename": "ContactController.php", "line": "537"}, "connection": "omx_sass_systam_db", "start_percent": 92.526, "width_percent": 7.474}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}, "App\\Models\\LeadStage": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLeadStage.php&line=1", "ajax": false, "filename": "LeadStage.php", "line": "?"}}, "App\\Models\\Pipeline": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPipeline.php&line=1", "ajax": false, "filename": "Pipeline.php", "line": "?"}}}, "count": 4, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/contacts\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/api/leads/21", "status_code": "<pre class=sf-dump id=sf-dump-1755841532 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1755841532\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-472307469 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-472307469\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2009728595 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2009728595\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6ImRtV2RPRXoweHM2Z05UaFlsczRPL3c9PSIsInZhbHVlIjoidytIQXdEdmthZmFXTlpnbXp6REpXTkFMa1ZJRXo0anJSdDZrbXNQaWJYcjgwMHVuSmNQQjlWZFQwdGRPL0tsOTZjVW5Sa3htVWh0L1lVWUJCRlZSMks4QnZ2Vy90dm9WTTVxM1lCOCt4RHhuejBmbXFSVkRVN2JRczNKdWVrd2dpRGF5WTJqekd3Q25WRldtaER4SXNnNXdFN3pKUnhJT1B1VDFHMVN1MzVzYmZIbkNXR3oyZk9BS0tnSDlzY1UvTm1XZmYvUm1heHlibWxiMzZZa0xBaElIZlVCQnM5VWFzNmtZZzlCNDFyRVhPZjk0ajhKci9WcnZsMEJHWDlWSEE2UDU1akx5UEgyTWZDdWNINk5ZODNIQ1ZvVmx1amZoQzlhQy9kQkNOVnJPYU1zRE0wMFdpYzFIQTRvM2xNOXJFclJ1VG5paDJ2TGFSK3ZDbDhMcThIR3g4QXQyczFXTTNoazc2WHkwQWZUbzJJNTdEVzEvK3lyQjJBVkFORHlhTVBtbnliNnhQdXdXalJQTTJidkJFTlJjTDg4UGVKTk42Vm1mVmQvRjh2U0dPbkdOeTJTK01XcElPMkRkZTkrWWpJamRRMHpFSFo3MFNWS1JaKzMwR1dZUm5HV0lteFpDbDFkUFhRSHJaWnpLRzVYSGdmRzUrUHZKeUo0QzJwWU8iLCJtYWMiOiI0MmRlOTA2MTMyYTllZDdiYTQxNmJlNTRmNjM3NWM4NGU3N2YyZjA3OTE5NDQzZjE4NTFlOTAyODU1ODE5NzI3IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Ik9oZTVKMld1Y1QrL2V6d2xDOFpPbnc9PSIsInZhbHVlIjoiMElUZjJTcW5aZXRncitXaUVzVE9SalZ0bHd3UHpTV0l4L2NnSU9Fc3pMWFlSaDVSK1FNbXpoMHN2a3M0d081dlFYdkFPRm9jNlRaYllGYzUvZnpPTmZxc3NmQmpqclg2RTRiMW5XM1RCUXhXVXorRjVybVBQeHQ2SHdBSEVmQnpyb3Fpa1VHMlFESmZUczlSZnovZGFHTStGYVhEclg0OTJHeXR3QktMS3dtMm5tbnAyZCtxR0ZZUDV6c1B0Y3lLVFRaNzN3ZXR3MG5pMkV5aGo0RDBDdDVIZ0szUDBndTBHT1dFZTNhVGc3NWlmTGIzTS9hRFBnNk5ndHdQTHFKS3ZzK1YzcnlWczdBZFRIcUY5clFiZktESStiZ1pMNGx4dUg5RWxINTBPMnZRUGMrVzQwTmY2TVNBUnVudVRJYkRJaHdLM0VuVEhoVGNCUHNsTS8wWUJVV3dKNno4eVhSbWkzenhCVVRGc0dLc3F3NXRwQVBRY1ZlTnJ2Nm1lUjV3WGE1bUx2ZE52UzVDRk52bGc4RTBTTlVzNVJ6VHA0eFExYXNoZER6bzZwMXB5UVZ6QkE2N1h2YkJReU9Qem5OUzdRenJLU2lxZXNlckozZEhoL08wUFJIdmxyRjBpaER0SlNMMCtUWGdMeDRvTStybExLeFZlSUJnQmdZdkp1c28iLCJtYWMiOiJlMTYyNjIzYzRkNTMwZTYwNzBhZjI0OTIzYWU1ZTc2OTZlYTEyYTAzODgxNTUzMjdmNTAxYTcxZjJlMmNmNjU1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1737670661 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1XDrxTWmYv3gorVrXvmSodhuVHNiE4Wue14QMI4v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1737670661\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1011901961 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 06:37:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlhoZGdWZmlUSmJDYjJrSTZlekEvZUE9PSIsInZhbHVlIjoid3RISHN3L0pQeHFreXMzSmI0SlBSUWUxRTIvZUtVSEF5bU5RUnJsakZGa2J2L2dHa3cwc2hRR2FsVkVlV2QyZ0RYZG11dDFNMFJMNGJCeXBhMVlUM3hCOG1qbjdua2xkSS9veWVldWNQYU13OU5MalVVdlk1eUZsc1V6bjN3OG9BS0Q4ZXpIeTVLTmZqT25PQms3SnBjQnltSG91aU9uYnhmSzQvTXdJanpTQmxjaHpPL01sLzAzemJIRGZnc3hNakt1NXg3UUVDSnZ3UjE4Nmx5T1BNV3VFWUNTQnJlRUtLeHZ5OUs0d0VydXpqS1JvQmpSKzNtUkI4L1NXNzczcEZ3UlFVa050MXE4SkZnVDhqU1hIUlRSVEpNbGR2UDJrbGJqblZLYkFvNDhFVlp0Uk1iNFNkVTZjTzBGeE9DUWFBSHZ5ZzBWQloxOUxtNjBlWFR6NVBValZhOEN5eUF4bDJwYmVVakYydEcyQ21ZWldNQktyVTJNSy9jM1p5THJnWHY5UGZHQjF1T1hzS0JmNjNzbE1CRC9Ya3BvM3RncmdyNjNzMXBGZkRBU0FjZ2MxOEV1MUZOZjFudHlCYVZGZC9kV0RaWEx3UnNWQ0pJNDBaMTk1VTlxOVdHSTBMQ2VtZ01scFdKWkVxL3RtY255Yk1heWFpVVNXeEtPYmVNVEYiLCJtYWMiOiJkODhjMmM2Y2ZhMTcyMzYyODhkYmJkNDE2OWViMDdlZDIzODVhMTEyNGQ2OTM5ZjJkMWRiOTlmMjU5ODZkNGJmIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:37:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InA2UDlzdVVpNDFtVzY1TklLYi9wUnc9PSIsInZhbHVlIjoiV1dLWVdXNGlGNnFzTlkzdkVPQ054eHZTOGtxRTA4TDI2dTBsekdPdjBUVEIrcEx1Vk9TSE1MYWVYOHpHR3E4bk1kbHd4NStPajZrU2NHK1c1elllZk9pU2FtV3JuLzNUUEJYZ0RVOWJLZFNsQitETG9Jc1M4WWpXQ2hGK3dVZFhrdU1LeHU5cHAyVzEvRkZhdnRpNzdrMGNVV2l6SDZxS2QzR0NpZkpKc09NLzVUVXdJNmVHVXNUenV3ZEN5RWdPZ3FRb2FaekNFbzZlQnRFeG5zbnZvWW5sK2FRUkdWZ0hnYnFoNXVDUmZOb25PUGxKazUzL002anlJTWV2U0wyeE9aeXp3c1h1eWdzTDhETW5wYU5xS1plM1JGdDVZakZhbFozRnhRYWhWWkdnL2cwakJjbGZRQVNVdGNmQWVQcXVTeWx4blVWa2pPcnJjREFrVDFBN3lQYk5UeUsrMGEvZ0pJOU5ORm1LZm5vY0RqenBjQ2wvNTBCVUhKUEJMQk00SjEvenRZRXlYL3cxZHFCRkpLYm40OEdwZlllMWtqalVuYWlpT1VieTlFQS85WDlXMEZaQnVPbGpHeUhoV3ZxYlRsNWgreEFzaTZyVEFNaEpYTE5oZXpXcWpKeU9PTnBYZkcyMTVNZXpIK1NvVFJxNE1CWm1VR3Fzd1I4Q2RwVEQiLCJtYWMiOiI5ODliOWRiZDM2MTY2MWZiYTZmN2RhYjU3YzQ2NmRiZTFlNmY1MWEzZTQ5ZDNhOTYzZTNhOTM0MGIyODRjZTk5IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:37:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlhoZGdWZmlUSmJDYjJrSTZlekEvZUE9PSIsInZhbHVlIjoid3RISHN3L0pQeHFreXMzSmI0SlBSUWUxRTIvZUtVSEF5bU5RUnJsakZGa2J2L2dHa3cwc2hRR2FsVkVlV2QyZ0RYZG11dDFNMFJMNGJCeXBhMVlUM3hCOG1qbjdua2xkSS9veWVldWNQYU13OU5MalVVdlk1eUZsc1V6bjN3OG9BS0Q4ZXpIeTVLTmZqT25PQms3SnBjQnltSG91aU9uYnhmSzQvTXdJanpTQmxjaHpPL01sLzAzemJIRGZnc3hNakt1NXg3UUVDSnZ3UjE4Nmx5T1BNV3VFWUNTQnJlRUtLeHZ5OUs0d0VydXpqS1JvQmpSKzNtUkI4L1NXNzczcEZ3UlFVa050MXE4SkZnVDhqU1hIUlRSVEpNbGR2UDJrbGJqblZLYkFvNDhFVlp0Uk1iNFNkVTZjTzBGeE9DUWFBSHZ5ZzBWQloxOUxtNjBlWFR6NVBValZhOEN5eUF4bDJwYmVVakYydEcyQ21ZWldNQktyVTJNSy9jM1p5THJnWHY5UGZHQjF1T1hzS0JmNjNzbE1CRC9Ya3BvM3RncmdyNjNzMXBGZkRBU0FjZ2MxOEV1MUZOZjFudHlCYVZGZC9kV0RaWEx3UnNWQ0pJNDBaMTk1VTlxOVdHSTBMQ2VtZ01scFdKWkVxL3RtY255Yk1heWFpVVNXeEtPYmVNVEYiLCJtYWMiOiJkODhjMmM2Y2ZhMTcyMzYyODhkYmJkNDE2OWViMDdlZDIzODVhMTEyNGQ2OTM5ZjJkMWRiOTlmMjU5ODZkNGJmIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:37:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InA2UDlzdVVpNDFtVzY1TklLYi9wUnc9PSIsInZhbHVlIjoiV1dLWVdXNGlGNnFzTlkzdkVPQ054eHZTOGtxRTA4TDI2dTBsekdPdjBUVEIrcEx1Vk9TSE1MYWVYOHpHR3E4bk1kbHd4NStPajZrU2NHK1c1elllZk9pU2FtV3JuLzNUUEJYZ0RVOWJLZFNsQitETG9Jc1M4WWpXQ2hGK3dVZFhrdU1LeHU5cHAyVzEvRkZhdnRpNzdrMGNVV2l6SDZxS2QzR0NpZkpKc09NLzVUVXdJNmVHVXNUenV3ZEN5RWdPZ3FRb2FaekNFbzZlQnRFeG5zbnZvWW5sK2FRUkdWZ0hnYnFoNXVDUmZOb25PUGxKazUzL002anlJTWV2U0wyeE9aeXp3c1h1eWdzTDhETW5wYU5xS1plM1JGdDVZakZhbFozRnhRYWhWWkdnL2cwakJjbGZRQVNVdGNmQWVQcXVTeWx4blVWa2pPcnJjREFrVDFBN3lQYk5UeUsrMGEvZ0pJOU5ORm1LZm5vY0RqenBjQ2wvNTBCVUhKUEJMQk00SjEvenRZRXlYL3cxZHFCRkpLYm40OEdwZlllMWtqalVuYWlpT1VieTlFQS85WDlXMEZaQnVPbGpHeUhoV3ZxYlRsNWgreEFzaTZyVEFNaEpYTE5oZXpXcWpKeU9PTnBYZkcyMTVNZXpIK1NvVFJxNE1CWm1VR3Fzd1I4Q2RwVEQiLCJtYWMiOiI5ODliOWRiZDM2MTY2MWZiYTZmN2RhYjU3YzQ2NmRiZTFlNmY1MWEzZTQ5ZDNhOTYzZTNhOTM0MGIyODRjZTk5IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:37:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1011901961\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-381835209 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-381835209\", {\"maxDepth\":0})</script>\n"}}