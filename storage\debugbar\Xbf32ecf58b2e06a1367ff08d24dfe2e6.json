{"__meta": {"id": "Xbf32ecf58b2e06a1367ff08d24dfe2e6", "datetime": "2025-07-29 06:37:38", "utime": **********.321507, "method": "GET", "uri": "/api/leads/21", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753771057.395978, "end": **********.321526, "duration": 0.9255480766296387, "duration_str": "926ms", "measures": [{"label": "Booting", "start": 1753771057.395978, "relative_start": 0, "end": **********.196963, "relative_end": **********.196963, "duration": 0.8009850978851318, "duration_str": "801ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.196976, "relative_start": 0.8009979724884033, "end": **********.321528, "relative_end": 1.9073486328125e-06, "duration": 0.12455201148986816, "duration_str": "125ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46081216, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/leads/{id}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ContactController@getLeadForPreview", "namespace": null, "prefix": "", "where": [], "as": "api.leads.show", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=531\" onclick=\"\">app/Http/Controllers/ContactController.php:531-567</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.021400000000000002, "accumulated_duration_str": "21.4ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.250928, "duration": 0.01776, "duration_str": "17.76ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 0, "width_percent": 82.991}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.2831378, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 82.991, "width_percent": 5.327}, {"sql": "select * from `leads` where `id` = '21' and `created_by` = 84 limit 1", "type": "query", "params": [], "bindings": ["21", "84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 537}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.288835, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:537", "source": "app/Http/Controllers/ContactController.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=537", "ajax": false, "filename": "ContactController.php", "line": "537"}, "connection": "omx_sass_systam_db", "start_percent": 88.318, "width_percent": 4.206}, {"sql": "select * from `lead_stages` where `lead_stages`.`id` in (118)", "type": "query", "params": [], "bindings": ["118"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 537}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.296855, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:537", "source": "app/Http/Controllers/ContactController.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=537", "ajax": false, "filename": "ContactController.php", "line": "537"}, "connection": "omx_sass_systam_db", "start_percent": 92.523, "width_percent": 4.252}, {"sql": "select * from `pipelines` where `pipelines`.`id` in (30)", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 537}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.3023431, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:537", "source": "app/Http/Controllers/ContactController.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=537", "ajax": false, "filename": "ContactController.php", "line": "537"}, "connection": "omx_sass_systam_db", "start_percent": 96.776, "width_percent": 3.224}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}, "App\\Models\\LeadStage": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLeadStage.php&line=1", "ajax": false, "filename": "LeadStage.php", "line": "?"}}, "App\\Models\\Pipeline": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPipeline.php&line=1", "ajax": false, "filename": "Pipeline.php", "line": "?"}}}, "count": 4, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/contacts\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/api/leads/21", "status_code": "<pre class=sf-dump id=sf-dump-1593598371 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1593598371\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1363347157 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1363347157\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1223383253 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1223383253\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IkNFYk53NEJhS2RrbGZuWnZ0a2IwekE9PSIsInZhbHVlIjoiNHUxczJvUUZlaXFKeWFuSHFkdUNrZTMrd1k2Z21QVjlRd2FzYmlrSG1XYjZuN1BkVENFSkV4WjErdVZnaEN2Z1hFQ3dRQUdsOW96ZUplbFQrb1BTZElGWXZESGY5MmlBMlhaRWVQZ0lndXNTdXAyM3UwWmFUbHV6UlRnckdTYW1qVHdQeE1hVEdycGlDMUlaNlhRbXhoUll2NFZFajdOVFZqR2dvckNRWWVQcUtzVkxKT3ZSTzNXTG5ManBxNThmUHI5ck90dGJpb1prTW1JK1NhWjJkQlJVTWd1OUNGWlQyK2FKZkhLVTVVRkhsTzB5Q3VzeCtManVZcG9rdG85N1lFQWJEOWVkU1dtUFEzcG1sYW0wSFJvclRELytGcEhYSkJ6TDAvRWxTalg1UHZ5RWY5YzhrNkZ2SmVxOXJBT0NHNitjWmlhWGxVQkFWQVNDRzF4b1dlOENuTUhBcmpaR2pCNGw1TlBtNnI0NFpOa29PSjRiMXJLWlliM2Y2UjJtU2Y4OEQvZ25nNC80Ujk4Uy9KZTlHaktZQjZiekNUdW9HQW93d1ZHc0hhN1BVYmQrZk04V0dsdzlZL2pHbjBaYjc3TGd2QUxLaUpuM09aSUkxZnByS1JxVGZNaStzM0dDbVgrdlZ0UGRrQXBIT3JhamZERllIYnhWQjlVa2FyT2MiLCJtYWMiOiI0NzVhNTlhOTNkZjUzOThhYzc3MTFlOWQ1YTQ4ZGM0OTUwYTY0NGIyNTcyMzE0YTlmN2VmNjFlOGFkNmY4MDQwIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IjZ6a2JZeWMwenNvV0o4VDRuQkc3dmc9PSIsInZhbHVlIjoiM0Rha1RBU0I4SCtsZjBSdU9SNXZjWnlZOXFyN0g3V1FHY3J2MngvYnF1V3pCS3BMc0toZm5icTFUcksrWndVTUdGNEtCSGdaWWVUY0tiaTB0NVozRXpzTm1sVGx5b1NYM3hrcjJMSmVTV1kwOW1aenljYkFSQWM1SVo3bXZUMnNseXNIT3JSMklUYU9Bb3VjY2cvelcxWnhJbTk4MVpNdHp4cGNLRjBYVHZaTXhiTzlWYm9xMTljRVZUL0g5OTJBR2xDNXBRNHRSWHRIeCtFZWs1RkZRQWluMjJ6STFRWFR4OGRhS3Z3aUFjVyt6WDExT3FRVCtYUThoTTM1UlgrWDVsUGVuckt3T2c5b3c3K2NpMzQ5MXBpRFQ2YmZ6cmRGdzlQZlpabWtjYWx3VHRDM1ZhYnpVWWk2VTdjVWtNUmFjZUhHYUlPc2FjQnY4UkpwWDJHa3FjeXhYNXl4OWwxMW1RU0FSYXpkbEZUaXRBdUo1cUNmR3EwWnJuL2lJUm80cE1vMk9YdXBvTmVNT3RuQnlqU3Npb1ZzTTFMWVN2YnNIRnpNMzVZWURtL0hLMy84ODYrajZrYjF2WGU3ZXpock9NQ0NpL3ZTSTJDZUJoTGp4ZDZYaXZYWXFvM20rUk9zWG9qeEx2TVBJNHRVSkptSFRCYlp1UkJRVmZ2NTVBUlMiLCJtYWMiOiI1OGU4MmMzNWRiNDViMzAwZDE0ZjY3MDgyYjAzODM2NjJmNjU1MzEwZWU4YWRjZDJiNGM4MjVlMTAxY2Y1OGQ2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1627515513 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1XDrxTWmYv3gorVrXvmSodhuVHNiE4Wue14QMI4v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1627515513\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1613201002 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 06:37:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImRtWHhwUWxaN0k1OGlWMVpRYkh3TkE9PSIsInZhbHVlIjoiY0djUjJUQWNsenJRSzI0bE9IcWk0N0NwUHhUTlF1V0RsUkUzVFd0VlMzb3ZaWlAzQnVWU2ozVmJtNFBrVk1NR3N0SllwMDc5N3M4cXFDK1FxY2JkNzNkWXFVakEzVk00Mk5YcnpOK1NZeDNqendpbDEzNWp1enkyUFUrcEdYM3IwR2dCeTgwSVBuTHowb3EwRlBhZkNqaTJRcFlPUkRFelBVenJpUnpHTEFoU09pUWs4OXc0SVFBN3IzVk1PQmVxcnE1ZnBYWEhuand4ZlpEdTFlays3UWhFYzE5ODBNVFRQUVhpaWNFa3ViR2tMMGZBZC9UYVlSTTN1b3RuREphTk9pYzVQeURudjNZK05GRmZYZy8yN2RWMVRQUU9DRWVGS3BGUGFPZjFLUDVoQVR4QkVRUkpYTWRWWUVLdGdGcHk4MnFSWnN2MDdLeHQ5eWYyUjZWb1kyeFZrbXhMRnZ1NE0rSEZKQ3RhaVNwSTdXOGkrOWJWQjU3bjM2UnZReTRmT0FsRkE5WnNTYnJxTWs5MURTTU4zMUFTYkdmcHNlWmpqVkFtRjk0WldTeTArYVQ2OVJEdzIrd2NnS0QvUGZRMENlTHBTSHBVOEJxTWxrR1JIUnV0a0U2b0h5RGZGd3dxdWc3L00xdThhNzZFbVZTalVVdXR1L1NabnhFMWVSNUciLCJtYWMiOiIzOTNmODk2ZTBiMTg2NWJjYzU4MGFhZDM4ZmI4YmE5NGE0ZWVlOGQ0YjFkMzJjYzQ4YmYxMzIwZTVhNTgxYWY5IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:37:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlkxMkxrNHI2ZU9FUXp0SDFZZm1GcFE9PSIsInZhbHVlIjoiWmI5QU9lVjZUTE9ZM2pNMG4xcU12SldrRHZHTi9RUGJrSkMxb2Noa1c0U3BRTFc5amNWQW0wb2ZGOWwyM0dMbE1sMEdUV1BrVG91ay9yTC9VdWI2bmt3REdtOVRkb2hsc3pPS1UvREVYa3RkUnlxVjhLYjVFK1pDYWlsUFBhdFRJOXUyaytZemllSFlUWU9zcUloS2VESHpHTVZXUDZGTVl3U3hWWW1JSWxqMkFURXgrRHVVUkhZL1pHSVUxSXdWZ3g4bzVNZnM5S0NwT0VSTUpxNFo3Uk93RXNwTGx5enZuYnB6WDhlbGR4S2xpcHIwQWNDMVc5d0pwNk9BTzFjN1FkVm1hUFdwUTJ1clMxN1VJY2MwaHVwRDZSR3M0T2FGc0IxMy9xVHdmL3NsTWlRMU9SZkI5aDJta1VHcy9sdnExSDhXU2JPcDRlMUlyVEs3VmgzVGZlbzBGL3FVMk0zSG5sYm5pRkhhZTJLQzd2WEpFRjROYXRYbjFVSlRRMGYvUU56WHNOZFEvbkthVElFazd1dUlBV2pKSkV6RmcvNTZhUlpjZEcrK3ZpcHBrcGNuc1NiQks5WnVxWldSYVM4NXFBcE9OL3R3U0hTNkhUN3ByNC9Md21CSHI3a015cTZFSUhzU0o1V1lqbDBFZWdVTFN5bTJFVzl3L0VyQUdHaFkiLCJtYWMiOiJhNTA2MmFlYmRiZjgxNGJjZTkwMGJlOGRkN2Y4NWI0MmIwMmVkNDA3NzRmMDU0YzdjOTAzNzk2NGEzZDdmNTIwIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:37:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImRtWHhwUWxaN0k1OGlWMVpRYkh3TkE9PSIsInZhbHVlIjoiY0djUjJUQWNsenJRSzI0bE9IcWk0N0NwUHhUTlF1V0RsUkUzVFd0VlMzb3ZaWlAzQnVWU2ozVmJtNFBrVk1NR3N0SllwMDc5N3M4cXFDK1FxY2JkNzNkWXFVakEzVk00Mk5YcnpOK1NZeDNqendpbDEzNWp1enkyUFUrcEdYM3IwR2dCeTgwSVBuTHowb3EwRlBhZkNqaTJRcFlPUkRFelBVenJpUnpHTEFoU09pUWs4OXc0SVFBN3IzVk1PQmVxcnE1ZnBYWEhuand4ZlpEdTFlays3UWhFYzE5ODBNVFRQUVhpaWNFa3ViR2tMMGZBZC9UYVlSTTN1b3RuREphTk9pYzVQeURudjNZK05GRmZYZy8yN2RWMVRQUU9DRWVGS3BGUGFPZjFLUDVoQVR4QkVRUkpYTWRWWUVLdGdGcHk4MnFSWnN2MDdLeHQ5eWYyUjZWb1kyeFZrbXhMRnZ1NE0rSEZKQ3RhaVNwSTdXOGkrOWJWQjU3bjM2UnZReTRmT0FsRkE5WnNTYnJxTWs5MURTTU4zMUFTYkdmcHNlWmpqVkFtRjk0WldTeTArYVQ2OVJEdzIrd2NnS0QvUGZRMENlTHBTSHBVOEJxTWxrR1JIUnV0a0U2b0h5RGZGd3dxdWc3L00xdThhNzZFbVZTalVVdXR1L1NabnhFMWVSNUciLCJtYWMiOiIzOTNmODk2ZTBiMTg2NWJjYzU4MGFhZDM4ZmI4YmE5NGE0ZWVlOGQ0YjFkMzJjYzQ4YmYxMzIwZTVhNTgxYWY5IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:37:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlkxMkxrNHI2ZU9FUXp0SDFZZm1GcFE9PSIsInZhbHVlIjoiWmI5QU9lVjZUTE9ZM2pNMG4xcU12SldrRHZHTi9RUGJrSkMxb2Noa1c0U3BRTFc5amNWQW0wb2ZGOWwyM0dMbE1sMEdUV1BrVG91ay9yTC9VdWI2bmt3REdtOVRkb2hsc3pPS1UvREVYa3RkUnlxVjhLYjVFK1pDYWlsUFBhdFRJOXUyaytZemllSFlUWU9zcUloS2VESHpHTVZXUDZGTVl3U3hWWW1JSWxqMkFURXgrRHVVUkhZL1pHSVUxSXdWZ3g4bzVNZnM5S0NwT0VSTUpxNFo3Uk93RXNwTGx5enZuYnB6WDhlbGR4S2xpcHIwQWNDMVc5d0pwNk9BTzFjN1FkVm1hUFdwUTJ1clMxN1VJY2MwaHVwRDZSR3M0T2FGc0IxMy9xVHdmL3NsTWlRMU9SZkI5aDJta1VHcy9sdnExSDhXU2JPcDRlMUlyVEs3VmgzVGZlbzBGL3FVMk0zSG5sYm5pRkhhZTJLQzd2WEpFRjROYXRYbjFVSlRRMGYvUU56WHNOZFEvbkthVElFazd1dUlBV2pKSkV6RmcvNTZhUlpjZEcrK3ZpcHBrcGNuc1NiQks5WnVxWldSYVM4NXFBcE9OL3R3U0hTNkhUN3ByNC9Md21CSHI3a015cTZFSUhzU0o1V1lqbDBFZWdVTFN5bTJFVzl3L0VyQUdHaFkiLCJtYWMiOiJhNTA2MmFlYmRiZjgxNGJjZTkwMGJlOGRkN2Y4NWI0MmIwMmVkNDA3NzRmMDU0YzdjOTAzNzk2NGEzZDdmNTIwIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:37:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1613201002\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1367136264 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1367136264\", {\"maxDepth\":0})</script>\n"}}