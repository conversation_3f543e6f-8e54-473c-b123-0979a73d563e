{"__meta": {"id": "X6ab1fced46b19e5baf8a49b866c12404", "datetime": "2025-07-29 06:46:12", "utime": **********.597608, "method": "GET", "uri": "/calendar-events/available", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 3, "messages": [{"message": "[06:46:12] LOG.info: Getting available events for user: 84", "message_html": null, "is_string": false, "label": "info", "time": **********.568445, "xdebug_link": null, "collector": "log"}, {"message": "[06:46:12] LOG.info: Getting all future events", "message_html": null, "is_string": false, "label": "info", "time": **********.57104, "xdebug_link": null, "collector": "log"}, {"message": "[06:46:12] LOG.info: Found 7 events", "message_html": null, "is_string": false, "label": "info", "time": **********.580408, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753771571.476758, "end": **********.597663, "duration": 1.1209049224853516, "duration_str": "1.12s", "measures": [{"label": "Booting", "start": 1753771571.476758, "relative_start": 0, "end": **********.420148, "relative_end": **********.420148, "duration": 0.943389892578125, "duration_str": "943ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.420181, "relative_start": 0.9434230327606201, "end": **********.597666, "relative_end": 3.0994415283203125e-06, "duration": 0.17748498916625977, "duration_str": "177ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46609104, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET calendar-events/available", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\CalendarEventController@getAvailableEvents", "as": "calendar-events.available", "namespace": null, "prefix": "/calendar-events", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FCalendarEventController.php&line=576\" onclick=\"\">app/Http/Controllers/CalendarEventController.php:576-649</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01436, "accumulated_duration_str": "14.36ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5231502, "duration": 0.01047, "duration_str": "10.47ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 0, "width_percent": 72.911}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.558543, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 72.911, "width_percent": 8.426}, {"sql": "select `id`, `title`, `duration`, `location`, `physical_address`, `meet_link` from `calendar_events` where `created_by` = 84 and `end_date` >= '2025-07-29 06:46:12' order by `start_date` asc", "type": "query", "params": [], "bindings": ["84", "2025-07-29 06:46:12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/CalendarEventController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\CalendarEventController.php", "line": 600}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.571491, "duration": 0.00268, "duration_str": "2.68ms", "memory": 0, "memory_str": null, "filename": "CalendarEventController.php:600", "source": "app/Http/Controllers/CalendarEventController.php:600", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FCalendarEventController.php&line=600", "ajax": false, "filename": "CalendarEventController.php", "line": "600"}, "connection": "omx_sass_systam_db", "start_percent": 81.337, "width_percent": 18.663}]}, "models": {"data": {"App\\Models\\CalendarEvent": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FCalendarEvent.php&line=1", "ajax": false, "filename": "CalendarEvent.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 8, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/contacts\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/calendar-events/available", "status_code": "<pre class=sf-dump id=sf-dump-214261210 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-214261210\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-764638433 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-764638433\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2111947833 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2111947833\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-980209085 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6InNRQ1l3M09XOEJTS2Z0Q2N6RHpTQmc9PSIsInZhbHVlIjoiSWY2YlpSVjlQbDdyMEVidTJKS0hiNldWVnJ6TjdUK0ZSdi9IOFF1L2ZuSkNTZ0VWYTZWUXdHUmU2ck1UUExreStKWWI4NU1PK2M2OTI3YmJacGNmaGk1R05BWDNsVERWTG5NY3ptanIrU2ltbmtsWlI4dmoydzR1S1duN1V1cVpYdS8vRytyNXlxMlJmTnkxNjBnQlhrZ1Y3aEkyazJyUjNwTE1mbmIvU1U2SDloaTRqa1BnM3BJamE5ZzFQMVp2N29ncjNhaHMwQUdXa2JRZTlmWXlyNUpuUHlqT2RKU3NvQjZwK3loanVpKzVnaUxCVldhK3E2dUZnYkRYSndlWDFIcnNGZXFUc3Nwcks3U1ppY0xWUXFnL0dQQjdIV1lRZDJqOFVqYWM1SGtiZDNhaFI5RXpDVWZHNkVMMW03MkZUcjFyL1pKelNjRGZCQ0ZOSE15VktQNThXY05QNEhpU2JDT2ZTUS9PNXBBVWJUbDBQbWFkMWxTbHczRjY0ZjJjQ3MvOGRwc0cwbTAzMG9xQ3JLbmxwZkkzbjE0cXZhd3VzSHkwSHdLVlJDbXlaaUVMUm1MR212SzlOZjJiKzkrWnBacnpVRzV1dWdxckRyR21KYkREcTlVY3k5cDVCb0xYc0RRWG90WElHckVjWmw3MGtROEFLUGhsdUFvNlkxZWsiLCJtYWMiOiI3NjMzOWJhOTAxOTQ4ZDI4N2MwMmJmOGY4NTU1YTM2YWMwZmEwMTdjZGZkNmE1MDI0YjYyN2EwOGYxNGM4ODNlIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6ImhQV1FORnp2cXh6bXFTYmRVd3RoU2c9PSIsInZhbHVlIjoiY0hqMUtwR1hPWXRrV1pTQzQvWEVZVGpKOS83dnBpckVJVG5ueU9NRUQ1RHA4N2JCemVmYUZ2aTNWVkdCbzZGYjdSVXFOWmRjb3lXMTg5dWJ0RGQ0YmlPeUE5T1JsZUVsTzFCclQzMGNuWEdRbzlhNkQ5dEV4bU5oelZTOGlkdlQxWnB0YURpMXFoOXN1dENITHhoazhHbnc1U3RDNnAvOVRMOVRkUWZDMTlxbHBTSnRJSk9FcTh5Qlg5QjZDb3QyM3JiS2E0TTJCWEFaWFZlQTkxRUIydHdrMHVtbUc2Q1Nza3d4QUNCTlNCK0RGVzNJUmh3LzdxQXV5TCt4eUtEb1g1REVVcU5XeHpNd0ZobWVMYkxON0dhZm9QUVUwSHhmSWZ4azFhZ0hUSkJOTnhlckNFRlVORnpOVmg0RGE1OW8wdDMxYnlIajZ0MzJJSHQyS1VCbzMyQlJ3SWEzU1NhWUFOYkVrMmlyMDBRdWNUQVRRczYxV0xaVk5MSWIwckU2TkFWWWFDUm4vSldzOU9sSlowN0NHRUNRSzZsV0YvV3FPc2szSk9iM1NjRzRDWlNPeER4YkQrZEdoSXlxVllCd0hpQ25YN2FGZjFVd2RiZG13VVZUcFhEbDhCK3NGVklBTndSWXJDN0Y1MXpERUFTTG5wZTFZcnJCWExVWTVHMVAiLCJtYWMiOiIyZjQ0ODk2YThhOWM1YjdkMTE2NDc4ZGI4ZTJlZWQ1N2Y5YTk1OGUyOWUzZGY0ODUxODUzNzBjYTJhODZmMjkxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-980209085\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2022676090 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1XDrxTWmYv3gorVrXvmSodhuVHNiE4Wue14QMI4v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2022676090\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-820518367 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 06:46:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InU3d3dFbitXczF4NWFWY0k1VmI0RHc9PSIsInZhbHVlIjoiVXVtbStvYStmamVqSDFoakpmUmxyVGFzRlN4MXRzNk0xekFkK0k3Mko4OHlDZFowajRMK2drdWI4QkxSZFM5RlR3Y053Mzg3Vnl3Ym1yd2R2eTZkSFRnM2NuVHA0SEpTaVM3TXFqcU9VNmFFdWV1TlZYcmg4UG5ZbDZWd0RVY1AwbTc1UVFZVzU1aWkreDNmS25lYW1uVkw5dlpmNEdoYmJIR0dMYXB5aWhiUnZubFpNSi9HbnNZUmgrZ05hcnd2OWI5bHZXL1dubGEyb0FvSTRySnRNbDhsejF3cG5ZSGl6bWVvNEpEMHRSU0RIaWMyMjBxZ0U4RmpnRmQ5dW44bzFjUmJ3aDgyaTRSdkhlbnZWS2tUU1h3WCtybXhNNXRxV1lhRXZjWmNPTTJjRVdKRzErS3Q4djdoSTROeXpwdDA4N2h1bmNTNXVVS2paUUk2VnRYSzRVb0dzbXphc3A5aFFjMnhqODU1am1zNExjZHpEMWprYk1tTzNEYys1YUpZenNqb0hvTlAxTWxnRjA5aGlWRkZTV1FqblpuN2VmTDJia3JYQkJyMGFzZ0hjU29aOU1hQ3JoeTFIZ3ViQzZSTUgvQUQvQnlWdDBEOHhWcEsvQ0o5WVBnWndBOEl5MWlnVGhEVGRZd1NFV1Jma1ZNQ2VrQ0NOalRQejVkUS9IZ0wiLCJtYWMiOiJjYjExMWI2OWY2Y2U5YjkyZDFjZmQ3MWI0ZTFmY2YzMmVmMmI2ZTg0NDA5MmI5NGFiNTNkNjBkYTJlOWRhOWFiIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:46:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ino3bkJzUzRWSGFGNXdvUDBINTNkeHc9PSIsInZhbHVlIjoiWUZsQUt6QVhUY3ZYVjY2dzd1dUVvWXNTSW03Q3p4VHlYUXFNa0lYVlJ6SlFrczM4QjFsdFFOVUt3RkNLVjVxUEZzRXh2Z0R0WHpLTkRxL0Fqa0laZERmaFNjNnYrdndJL1YxblBYamcxbCtqSTRqMklOdTRKcUtHR0szUnkxc2duRzhYZk5xN09HVzhnSzE4LzJtZ2RUS1dkdGl5cmIzeXpweWRNS3dTZ0xIb2c2YzZKR01CUCs0bDZxUzI5YUNsWm9mVW41Z0JEYVB0Qjl0WTdYNHNsZmNLK2RnOUk3UWZIeTBFVytoWktCOFBFZnRnQVFqMzBPNTlkTGJFZjFNVG1YdTNGdm9EYVlXRWFXOGQ4Y2ZMaXg5K09RcVVzUnNUVERrdmt3V21BT3Z4bjJLT3pHU1ZNRk91RDBBclJXcSsyV2ErMUFpeTVqeUtTMDNsV3pGUHJ2cE5JOXRvOHNjQVRGNUo2TzQyRGcxR1lJRVFhL05vQzhJNTRDNjRvdmJCbmVxK0s1SldXcWllamxKTmdIOTdVd0t4TVM1QVZBVlRSNGF5SURzM2ROQlRsampXMFlRMmZuKzlwVE5iQUhBVHRHUmludjNKLzF1Y2g1WjczQ2JKRnZJV2U4Ylp4ZjVIVG9GS282azNTQzE2RTRobENIbXhoU1U0STlEbFBBTmYiLCJtYWMiOiIwNGZjZmE3MThhYWZmMDY2YTkyM2Q2NWRiOGNhOWQzZjc1ODk1MjkzYzk4MjlmZjg1ZWVhMzAyM2Q3NmUxNjRlIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:46:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InU3d3dFbitXczF4NWFWY0k1VmI0RHc9PSIsInZhbHVlIjoiVXVtbStvYStmamVqSDFoakpmUmxyVGFzRlN4MXRzNk0xekFkK0k3Mko4OHlDZFowajRMK2drdWI4QkxSZFM5RlR3Y053Mzg3Vnl3Ym1yd2R2eTZkSFRnM2NuVHA0SEpTaVM3TXFqcU9VNmFFdWV1TlZYcmg4UG5ZbDZWd0RVY1AwbTc1UVFZVzU1aWkreDNmS25lYW1uVkw5dlpmNEdoYmJIR0dMYXB5aWhiUnZubFpNSi9HbnNZUmgrZ05hcnd2OWI5bHZXL1dubGEyb0FvSTRySnRNbDhsejF3cG5ZSGl6bWVvNEpEMHRSU0RIaWMyMjBxZ0U4RmpnRmQ5dW44bzFjUmJ3aDgyaTRSdkhlbnZWS2tUU1h3WCtybXhNNXRxV1lhRXZjWmNPTTJjRVdKRzErS3Q4djdoSTROeXpwdDA4N2h1bmNTNXVVS2paUUk2VnRYSzRVb0dzbXphc3A5aFFjMnhqODU1am1zNExjZHpEMWprYk1tTzNEYys1YUpZenNqb0hvTlAxTWxnRjA5aGlWRkZTV1FqblpuN2VmTDJia3JYQkJyMGFzZ0hjU29aOU1hQ3JoeTFIZ3ViQzZSTUgvQUQvQnlWdDBEOHhWcEsvQ0o5WVBnWndBOEl5MWlnVGhEVGRZd1NFV1Jma1ZNQ2VrQ0NOalRQejVkUS9IZ0wiLCJtYWMiOiJjYjExMWI2OWY2Y2U5YjkyZDFjZmQ3MWI0ZTFmY2YzMmVmMmI2ZTg0NDA5MmI5NGFiNTNkNjBkYTJlOWRhOWFiIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:46:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ino3bkJzUzRWSGFGNXdvUDBINTNkeHc9PSIsInZhbHVlIjoiWUZsQUt6QVhUY3ZYVjY2dzd1dUVvWXNTSW03Q3p4VHlYUXFNa0lYVlJ6SlFrczM4QjFsdFFOVUt3RkNLVjVxUEZzRXh2Z0R0WHpLTkRxL0Fqa0laZERmaFNjNnYrdndJL1YxblBYamcxbCtqSTRqMklOdTRKcUtHR0szUnkxc2duRzhYZk5xN09HVzhnSzE4LzJtZ2RUS1dkdGl5cmIzeXpweWRNS3dTZ0xIb2c2YzZKR01CUCs0bDZxUzI5YUNsWm9mVW41Z0JEYVB0Qjl0WTdYNHNsZmNLK2RnOUk3UWZIeTBFVytoWktCOFBFZnRnQVFqMzBPNTlkTGJFZjFNVG1YdTNGdm9EYVlXRWFXOGQ4Y2ZMaXg5K09RcVVzUnNUVERrdmt3V21BT3Z4bjJLT3pHU1ZNRk91RDBBclJXcSsyV2ErMUFpeTVqeUtTMDNsV3pGUHJ2cE5JOXRvOHNjQVRGNUo2TzQyRGcxR1lJRVFhL05vQzhJNTRDNjRvdmJCbmVxK0s1SldXcWllamxKTmdIOTdVd0t4TVM1QVZBVlRSNGF5SURzM2ROQlRsampXMFlRMmZuKzlwVE5iQUhBVHRHUmludjNKLzF1Y2g1WjczQ2JKRnZJV2U4Ylp4ZjVIVG9GS282azNTQzE2RTRobENIbXhoU1U0STlEbFBBTmYiLCJtYWMiOiIwNGZjZmE3MThhYWZmMDY2YTkyM2Q2NWRiOGNhOWQzZjc1ODk1MjkzYzk4MjlmZjg1ZWVhMzAyM2Q3NmUxNjRlIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:46:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-820518367\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-58848518 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-58848518\", {\"maxDepth\":0})</script>\n"}}