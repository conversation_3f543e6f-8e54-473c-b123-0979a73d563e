{"__meta": {"id": "Xbc460bb6b43f9916488aae905a3fbd7a", "datetime": "2025-07-29 07:15:42", "utime": **********.253505, "method": "GET", "uri": "/contact-groups-list", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753773340.736503, "end": **********.253549, "duration": 1.5170462131500244, "duration_str": "1.52s", "measures": [{"label": "Booting", "start": 1753773340.736503, "relative_start": 0, "end": **********.037949, "relative_end": **********.037949, "duration": 1.3014461994171143, "duration_str": "1.3s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.037978, "relative_start": 1.3014750480651855, "end": **********.253551, "relative_end": 1.9073486328125e-06, "duration": 0.21557307243347168, "duration_str": "216ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46649184, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET contact-groups-list", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\ContactGroupController@getContactGroups", "namespace": null, "prefix": "", "where": [], "as": "contact-groups.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactGroupController.php&line=133\" onclick=\"\">app/Http/Controllers/ContactGroupController.php:133-144</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01022, "accumulated_duration_str": "10.22ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.186161, "duration": 0.00647, "duration_str": "6.47ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 0, "width_percent": 63.307}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.212985, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 63.307, "width_percent": 10.861}, {"sql": "select `id`, `name` from `contact_groups` where `created_by` = 84 order by `name` asc", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ContactGroupController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactGroupController.php", "line": 138}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.220072, "duration": 0.00264, "duration_str": "2.64ms", "memory": 0, "memory_str": null, "filename": "ContactGroupController.php:138", "source": "app/Http/Controllers/ContactGroupController.php:138", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactGroupController.php&line=138", "ajax": false, "filename": "ContactGroupController.php", "line": "138"}, "connection": "omx_sass_systam_db", "start_percent": 74.168, "width_percent": 25.832}]}, "models": {"data": {"App\\Models\\ContactGroup": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FContactGroup.php&line=1", "ajax": false, "filename": "ContactGroup.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 7, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/contact-groups-list", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IitjWXhMRURKajdkMTVacDcvL0ZzSVE9PSIsInZhbHVlIjoiTHMxYjZ4UktFa0Q3NTFZd3NobnhZVjcrNTFtTTVMU0EyNUIrTzBhdHMzc3ptSXgwbmRtTzIrano2cVl2NEJGQzNxUG9aVjBWdFh6NVU5VUZFRnd1dG5OLytBN2VFVzhDWFdhMks0WU1OdWs5dVdwSXJTZEV3OXM5QUtqTURhU3ExbmhFSE1jMndDaThoK3V3aGlMUkFVeUhkaE5yVWpxK2lUNXllZTJtYzhLeHN0L2ZJeHZQbzJqWDVRTDRZeHFPOXZWTFVycFYvQnl2ZGNKLzhsbFdSOXNaOGllUDZJazZ0Q2tBWmhoWXh6K2dvYlNab3Z5WGdEdlZvR3RvU1Fza05aRms3bHY0YzNDZ3ZFWUJnTnRRdDltMTVyZkl1ak1NRURQTUVpZ09yUHpBNW94MHU2SlJ1Y0NIRk5XRENBdWNhMnd2ZVNxRVI0b1RQU084U3hFY2Ixb0U3V29MNTZ3RHIvaFg5NkpHTDQ4OTVuOTVya3ZRWmg3cERxZTNISW5ObVQ1NjFndlF1TC9iRldmZ1Q0WGdNdytFYmliMDRnZWNpaEFKeW1ydnM5b1ZveWVaUXY0V0hZSFhlYzJTeU94c1RCdTBHb1p0WWNWaENXeHpMVEY1dEU0TmI5Z3BUK0JYM3JOUERVVzVGd1pJUjk3M3E5RDlqVFFkNjl3NlNWSysiLCJtYWMiOiJiNDU0ZGM0YjE2ZDRiZGRhMzY0NzY0NjJiOTJiMDZkZThmYjQ1MDM4MzQ0MGRiZGRiMTRiNTg0Y2QzZTQ5MGIxIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IjhiaGgvOXZXZjNTQ1lBSzdVVW9uTXc9PSIsInZhbHVlIjoiVml2Y3VKWEsxdW4vOTdjVFkya1JGdERCWDUxRmtkejFseDNaV0h0V2svVG9xOHY2VWpoRXpKZVg5SmR3b3dSZjhGRDRiUkpzdWVKRCtSNlBFT2UvU3Jxd3BZYzlOcGpScjVlVzh3alNWdXI1QUVHbnZhNkd3c2NKZzkrekVGdkRxQWo0ZWNYZHdQTmtKU0VDMGVIYkliaUVlR1d2WDJqRUdYM3RURklMeEx4UXNJbEZvcm93WUlmWkt6RHU1Wm1TZURPYm5pbEtTSENoa1hiVUVzNU4yYkhSaEZyM0lRMkwxSTRlRm9vY1lnaEU2cjdaZ21hL2UxZVJUYXZLVlNwRllucmxqWmYrT29sMS9ZMmtwMVFPUmx1NmZod21CMGFwU1UvbVpEUldhaXd1K3FKWkRKbnBiRGg2a3lhSHE5NVNkQzF5ejhlZCswTVB4VzFKT2hpcDY0SnFLZ1IrcW01YVQvbHdJZURyK3BQb3dob0Fzd0pPSSswZ2JxSUxsSzl2VW81S3FmZGVNTy9BNUt5WURGWnhKYTVXckkrZXBUazllSFJvdzNZWDhQc2VpbjA0dDRsaitBZ3c2NWcxRWVKajBHeTVlSzZ0STEwWm1CenozekJTbkZjUHFBaEFHVFBRTmV4V0dqL0tGMTk0NHcydC9IOG9ZTTZtODRteFU5TnQiLCJtYWMiOiI1NTEwMTgyYzhmZjQzMWMxMTdiZDM2OWExYzQ0ZjlmMTA4Y2M5YzE0NzY0YTA2OTkwMDBiOWQ1MzMzOTgyNjRmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1129895242 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1XDrxTWmYv3gorVrXvmSodhuVHNiE4Wue14QMI4v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1129895242\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2036022857 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 07:15:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IldONUZkQWFOT0lYdW1lRHNMOFhqbGc9PSIsInZhbHVlIjoiWGRaQjdtUjZxZ3BVNjBPT3FSYVFtMTBjQUl2L0lRdWZTdWhQYVl2dTg2MkU1MHRGUndOei8wYWhhcmROS2k3SmFhZSszUVI3OGVCS1BvSGRHbWFFcWd1VEZjSlVBM3BidlFBZjZkdFZlUzJmRzY1M0wwY0V0Zm8vRDJ5ZmExSTBxOG1LNUtOSCtQa3RXSFMvcEY0a0R2TnpCWVlBY2pHZEdJYnQwTzZ0cUVaajRjUUZqNGhIQnJRa0U4d1pTTFpnaTFtMjBxODJiMGhEVmYzY0NPZmJqSEUzY2FqMlFPdW5XSGRCRjRvOWo2REEwMDVZRVpkdG5xa2tlNU0vMHBRRlZnVXZuMWxhTHJUQzBuRjIraldwczA0WDFGbWRHOE14OTd4VEVqVTh4dk9XQUdydE8veXhXQlJYcG1nQlo1U2lQdDF0a2Z2emJHSGFyaUFqa0pUSjJSU0s2L0xwSUdRUUJBU1BQRlE3RFhSM2M2RnY3RXRYRllKWWtVK3pVQW5WcW1tWjkxb2dmQW5xQTVZVUd4Z2ZIWnppd0FqN05pQmc5MEVyWVV5TVluWXk0alduRWhKQlVBUzJLeTZyQldaZkdjL1FGZlZ2bURBTkExRVFZNEpuZCtuNEIzNkNEUTd6RmN4TUtXek1tUWxmVVZDcGdETjRRcGQ5cWltZlFUckoiLCJtYWMiOiJjZDg0NDYxMWZjODgxOGU5NTEyOWViMzY2Y2EzZGFjZjFiODY5MTkzY2Y1ZjE1N2RmZmRlYTE2ZDcxNDk3NGEzIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 09:15:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjlaL1R1bng3anJIdzhTZURjdExwemc9PSIsInZhbHVlIjoiN2gzSWhNemJoVDMrWlpXcXpnaXpKdWtPSkhVajBRRG5PT2pLRGFqTWVVV0xLVUxVWUtPM0xULzltMEt3dUZiemt4TVpOcU1OajRrNnhCYnEwTVZod3VEbThHa3p6c05HeDlENFVnL2loNHZyOWZIdnBLQWFueXJRZWFQMVJMRWdoQU5xbXgzMmZrdTM1WGVCdWsyMmVFakc0c0NuRHMxWFgvQlltMmsycUo2cHhKaVJMd25DcWxMV0taQ0dtdjZUWkEyazhFYzUrWUFkM0ZvYUhpNy8wMmNkOG5OWml2b2hwUUJkVFZuU0pKamxyT2xuRGVvU21HTWdWbFJ2dmZ5dFkzY3RoMC9QL2l5MG1HRks4cUhkYzk2STI2ZU12cHdyc0JZKzAvMyttT050clh1RXVSekdOWGJtWXRpaWtPNjVic01hVUJjM1VyOEU5Vk04OVdKdnlsbDUwL0tiOXhJdng1QW1LMzNlakdZcVdZckZ4SVZuRWpmQkE2UHpySlhXczlrV21hNnI3eHpHSW12ZlJZbGhsdkZMaWxkWGt4T1o3TU5nMzcrWmVQV3gzSUR6YkY3N2dEK09CelpvMk5IRFdSYWFjZ0YvbCtXTGtHdXJHa2ZnRXlsY0IwTk9UVFRuY0k4Y2xFRld5U0NZaWhhRzJnbXFOZDZVTGc4emtHZHIiLCJtYWMiOiI0MTk0MDQ3NGQzYWUxMzAwNWQyYmZiNmRkYzI2ZmE2YWUzOGU4YmM0OTQ5Nzk2MzRmOTY3YTE5ZDU4Y2M4MzVjIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 09:15:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IldONUZkQWFOT0lYdW1lRHNMOFhqbGc9PSIsInZhbHVlIjoiWGRaQjdtUjZxZ3BVNjBPT3FSYVFtMTBjQUl2L0lRdWZTdWhQYVl2dTg2MkU1MHRGUndOei8wYWhhcmROS2k3SmFhZSszUVI3OGVCS1BvSGRHbWFFcWd1VEZjSlVBM3BidlFBZjZkdFZlUzJmRzY1M0wwY0V0Zm8vRDJ5ZmExSTBxOG1LNUtOSCtQa3RXSFMvcEY0a0R2TnpCWVlBY2pHZEdJYnQwTzZ0cUVaajRjUUZqNGhIQnJRa0U4d1pTTFpnaTFtMjBxODJiMGhEVmYzY0NPZmJqSEUzY2FqMlFPdW5XSGRCRjRvOWo2REEwMDVZRVpkdG5xa2tlNU0vMHBRRlZnVXZuMWxhTHJUQzBuRjIraldwczA0WDFGbWRHOE14OTd4VEVqVTh4dk9XQUdydE8veXhXQlJYcG1nQlo1U2lQdDF0a2Z2emJHSGFyaUFqa0pUSjJSU0s2L0xwSUdRUUJBU1BQRlE3RFhSM2M2RnY3RXRYRllKWWtVK3pVQW5WcW1tWjkxb2dmQW5xQTVZVUd4Z2ZIWnppd0FqN05pQmc5MEVyWVV5TVluWXk0alduRWhKQlVBUzJLeTZyQldaZkdjL1FGZlZ2bURBTkExRVFZNEpuZCtuNEIzNkNEUTd6RmN4TUtXek1tUWxmVVZDcGdETjRRcGQ5cWltZlFUckoiLCJtYWMiOiJjZDg0NDYxMWZjODgxOGU5NTEyOWViMzY2Y2EzZGFjZjFiODY5MTkzY2Y1ZjE1N2RmZmRlYTE2ZDcxNDk3NGEzIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 09:15:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjlaL1R1bng3anJIdzhTZURjdExwemc9PSIsInZhbHVlIjoiN2gzSWhNemJoVDMrWlpXcXpnaXpKdWtPSkhVajBRRG5PT2pLRGFqTWVVV0xLVUxVWUtPM0xULzltMEt3dUZiemt4TVpOcU1OajRrNnhCYnEwTVZod3VEbThHa3p6c05HeDlENFVnL2loNHZyOWZIdnBLQWFueXJRZWFQMVJMRWdoQU5xbXgzMmZrdTM1WGVCdWsyMmVFakc0c0NuRHMxWFgvQlltMmsycUo2cHhKaVJMd25DcWxMV0taQ0dtdjZUWkEyazhFYzUrWUFkM0ZvYUhpNy8wMmNkOG5OWml2b2hwUUJkVFZuU0pKamxyT2xuRGVvU21HTWdWbFJ2dmZ5dFkzY3RoMC9QL2l5MG1HRks4cUhkYzk2STI2ZU12cHdyc0JZKzAvMyttT050clh1RXVSekdOWGJtWXRpaWtPNjVic01hVUJjM1VyOEU5Vk04OVdKdnlsbDUwL0tiOXhJdng1QW1LMzNlakdZcVdZckZ4SVZuRWpmQkE2UHpySlhXczlrV21hNnI3eHpHSW12ZlJZbGhsdkZMaWxkWGt4T1o3TU5nMzcrWmVQV3gzSUR6YkY3N2dEK09CelpvMk5IRFdSYWFjZ0YvbCtXTGtHdXJHa2ZnRXlsY0IwTk9UVFRuY0k4Y2xFRld5U0NZaWhhRzJnbXFOZDZVTGc4emtHZHIiLCJtYWMiOiI0MTk0MDQ3NGQzYWUxMzAwNWQyYmZiNmRkYzI2ZmE2YWUzOGU4YmM0OTQ5Nzk2MzRmOTY3YTE5ZDU4Y2M4MzVjIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 09:15:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2036022857\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}