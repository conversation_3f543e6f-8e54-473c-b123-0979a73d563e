{"__meta": {"id": "X92b146893bf0b3166b15befd8fa81a43", "datetime": "2025-07-29 06:30:36", "utime": **********.994628, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753770634.513282, "end": **********.994693, "duration": 2.4814109802246094, "duration_str": "2.48s", "measures": [{"label": "Booting", "start": 1753770634.513282, "relative_start": 0, "end": **********.685398, "relative_end": **********.685398, "duration": 2.1721160411834717, "duration_str": "2.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.685448, "relative_start": 2.***************, "end": **********.994699, "relative_end": 5.9604644775390625e-06, "duration": 0.***************, "duration_str": "309ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2999\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1805 to 1811\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1805\" onclick=\"\">routes/web.php:1805-1811</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LemYR4Fje67VDPEyfVYbq0FpUkapfAmwSEUsTUMy", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-566024722 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-566024722\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-880058337 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-880058337\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-164808345 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-164808345\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1245275637 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1245275637\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1818775005 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1818775005\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-166473885 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 06:30:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlI0UllpSmRqZ3JEWkR1NVk0RWlUYVE9PSIsInZhbHVlIjoiS21Sb2NtMUVtVitRWDlGQkpsZlVaNllqRDFqQTBOSGVmQytkeldqRVEvUzJLQWhuQmMwNnVSVTZWYXEwVWl2R2RHdEU0WGNxd2ZkYmtKTTRSNXY3OGprTGF5bElsazJUNjcrZVBNQmViTjNsbk1RaEV3ZjE1KzZzdVBzVUNHZGhWTkk3Qmswb1loS2VWK1pPTlNjejgvRStLNlFGcExiUUNYc0RiNFYwNjVHdW9YaTR2cFU0OThMaEYzbjhyRHJSR3ltYkd6eVRQVjk0OFI2VEpBWDhXdjJ3UFdmSExxdW15VWt6ajNDTmd4YWw3aEJWU2pnTGVSQVEwZE5xa3pEeFpxZFVDWWNiMTdpcDYzai9iOXhWeGV4d3RvbU9DNG9Md0wwSC9pcDdSM3oxeEEzbUhyeUc0SEhJWitaanM2bEs3MHd4emcwNm9TYmRIV2JuUzFud0FHK2xhdk1oM29XelJVS3FpTHdXZjdPc2Q2QVZIeFJBemg1MHFsN1laZlhNbkh0UXQyVFZReXkvM1JmelBIVTQ4NTltNW9SMEl1dlc5Y1pUQmNpTFB0U1ZJUk53OXc2QVJ5SHpVU2Y4b0FJVmR6T1lIZmNDWjFLT1pNVnA4SEF6VmFiV2loQXhHU2paNnh3ZytVK3dldUdWS2FmZSsramxPUjFKTlNrNmRlMFEiLCJtYWMiOiJiOWQ2ZWY4ZjlhOTVmMWJkYTgxODBlZDZjY2I1MjA3NzdmNDgwZDQzMmI5MjQ2ZDM1ZmI2MTMxYTdkY2QzMTAyIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:30:36 GMT; Max-Age=7199; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImNrL3N0b0tiRzRmVGg5US9odkI1SlE9PSIsInZhbHVlIjoibkp4MkRja3BZemZkejRNbG5pbGFEZGRFNnFzT2h1cTRJa202eXRmSTJlc1NFNk14YnVwUXZxbGZlY2hXQytDSGJ1eWdxc3pLeWlqd0p1M1lPTm1lYk4rK2k5cmZjb3U5Mzd1cjlCbDJVVDcvNEJkK1lUeExub1lFMWFTOXRrNGZNajZyUVZwZGE0MGNISEdHakdrT1pISVVVbXRrbXVsMmxDajlwZENiRXQySTBmNGRyR1o3b1NlV2VTdXBnaElCejFNSlBjYXlPTHJuNGtlQXdYdG5lbXF6SnNpR25USzlNRnhSYXRIdS9wOGY2cUJQY2VzNG4yWkVTSFhVbGtHUU5pT1AwSXBocUhKMWp3NlJUYkZTd00vVXZjZTB6b2lscEhGUUNQaWkwN201elV4SGlvbmhhRGVYVVMvZDhvcjFGL1J3NW5RdUtSYTBmaG9KSUpiRHEvOWJaNFMxN3pBMXdUK1lQVTJyS09tTjZuRzZlMDd2MmFDbVMvUzBXZk5jdDcxZ0srdG1sQVVEUjFPVDZhWURMQTZOWlFkbWNPc3FkUDkzcXRMQWVLaVZvR2EyaEFFMFdpczUzQXRoSXN2R2pMOG9FT3hCS1k5a1BWWE9nUStlcjMzZ2hkMWJXb2pOVWFDeVM1ZG5la2VCZjRub0p1dFlPdHF2Z001OWdZZ0EiLCJtYWMiOiJkMmJlY2JmZGVhMTM2ZDFlNjk5YWNmYmYyZDExZGYyMWVmZGE4OTQ4ZmI5MWQ3NTUwOGU3OGQ0NmEwZmVjODA5IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:30:36 GMT; Max-Age=7199; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlI0UllpSmRqZ3JEWkR1NVk0RWlUYVE9PSIsInZhbHVlIjoiS21Sb2NtMUVtVitRWDlGQkpsZlVaNllqRDFqQTBOSGVmQytkeldqRVEvUzJLQWhuQmMwNnVSVTZWYXEwVWl2R2RHdEU0WGNxd2ZkYmtKTTRSNXY3OGprTGF5bElsazJUNjcrZVBNQmViTjNsbk1RaEV3ZjE1KzZzdVBzVUNHZGhWTkk3Qmswb1loS2VWK1pPTlNjejgvRStLNlFGcExiUUNYc0RiNFYwNjVHdW9YaTR2cFU0OThMaEYzbjhyRHJSR3ltYkd6eVRQVjk0OFI2VEpBWDhXdjJ3UFdmSExxdW15VWt6ajNDTmd4YWw3aEJWU2pnTGVSQVEwZE5xa3pEeFpxZFVDWWNiMTdpcDYzai9iOXhWeGV4d3RvbU9DNG9Md0wwSC9pcDdSM3oxeEEzbUhyeUc0SEhJWitaanM2bEs3MHd4emcwNm9TYmRIV2JuUzFud0FHK2xhdk1oM29XelJVS3FpTHdXZjdPc2Q2QVZIeFJBemg1MHFsN1laZlhNbkh0UXQyVFZReXkvM1JmelBIVTQ4NTltNW9SMEl1dlc5Y1pUQmNpTFB0U1ZJUk53OXc2QVJ5SHpVU2Y4b0FJVmR6T1lIZmNDWjFLT1pNVnA4SEF6VmFiV2loQXhHU2paNnh3ZytVK3dldUdWS2FmZSsramxPUjFKTlNrNmRlMFEiLCJtYWMiOiJiOWQ2ZWY4ZjlhOTVmMWJkYTgxODBlZDZjY2I1MjA3NzdmNDgwZDQzMmI5MjQ2ZDM1ZmI2MTMxYTdkY2QzMTAyIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:30:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImNrL3N0b0tiRzRmVGg5US9odkI1SlE9PSIsInZhbHVlIjoibkp4MkRja3BZemZkejRNbG5pbGFEZGRFNnFzT2h1cTRJa202eXRmSTJlc1NFNk14YnVwUXZxbGZlY2hXQytDSGJ1eWdxc3pLeWlqd0p1M1lPTm1lYk4rK2k5cmZjb3U5Mzd1cjlCbDJVVDcvNEJkK1lUeExub1lFMWFTOXRrNGZNajZyUVZwZGE0MGNISEdHakdrT1pISVVVbXRrbXVsMmxDajlwZENiRXQySTBmNGRyR1o3b1NlV2VTdXBnaElCejFNSlBjYXlPTHJuNGtlQXdYdG5lbXF6SnNpR25USzlNRnhSYXRIdS9wOGY2cUJQY2VzNG4yWkVTSFhVbGtHUU5pT1AwSXBocUhKMWp3NlJUYkZTd00vVXZjZTB6b2lscEhGUUNQaWkwN201elV4SGlvbmhhRGVYVVMvZDhvcjFGL1J3NW5RdUtSYTBmaG9KSUpiRHEvOWJaNFMxN3pBMXdUK1lQVTJyS09tTjZuRzZlMDd2MmFDbVMvUzBXZk5jdDcxZ0srdG1sQVVEUjFPVDZhWURMQTZOWlFkbWNPc3FkUDkzcXRMQWVLaVZvR2EyaEFFMFdpczUzQXRoSXN2R2pMOG9FT3hCS1k5a1BWWE9nUStlcjMzZ2hkMWJXb2pOVWFDeVM1ZG5la2VCZjRub0p1dFlPdHF2Z001OWdZZ0EiLCJtYWMiOiJkMmJlY2JmZGVhMTM2ZDFlNjk5YWNmYmYyZDExZGYyMWVmZGE4OTQ4ZmI5MWQ3NTUwOGU3OGQ0NmEwZmVjODA5IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:30:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-166473885\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LemYR4Fje67VDPEyfVYbq0FpUkapfAmwSEUsTUMy</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}