{"__meta": {"id": "X4a8381d330ce8c8da3f1d76388a08c60", "datetime": "2025-07-29 07:19:23", "utime": **********.062076, "method": "GET", "uri": "/contact-groups-list", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753773562.127924, "end": **********.0621, "duration": 0.934175968170166, "duration_str": "934ms", "measures": [{"label": "Booting", "start": 1753773562.127924, "relative_start": 0, "end": 1753773562.940772, "relative_end": 1753773562.940772, "duration": 0.8128480911254883, "duration_str": "813ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753773562.940785, "relative_start": 0.8128609657287598, "end": **********.062102, "relative_end": 2.1457672119140625e-06, "duration": 0.12131714820861816, "duration_str": "121ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46649568, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET contact-groups-list", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\ContactGroupController@getContactGroups", "namespace": null, "prefix": "", "where": [], "as": "contact-groups.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactGroupController.php&line=133\" onclick=\"\">app/Http/Controllers/ContactGroupController.php:133-144</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.015969999999999998, "accumulated_duration_str": "15.97ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.007559, "duration": 0.01453, "duration_str": "14.53ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 0, "width_percent": 90.983}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.042363, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 90.983, "width_percent": 5.322}, {"sql": "select `id`, `name` from `contact_groups` where `created_by` = 84 order by `name` asc", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ContactGroupController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactGroupController.php", "line": 138}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.047004, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "ContactGroupController.php:138", "source": "app/Http/Controllers/ContactGroupController.php:138", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactGroupController.php&line=138", "ajax": false, "filename": "ContactGroupController.php", "line": "138"}, "connection": "omx_sass_systam_db", "start_percent": 96.306, "width_percent": 3.694}]}, "models": {"data": {"App\\Models\\ContactGroup": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FContactGroup.php&line=1", "ajax": false, "filename": "ContactGroup.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 7, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/contact-groups-list", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6Im5tYTdtZkgwNThiRDBvK2FsQmZVZnc9PSIsInZhbHVlIjoiRHE2bmllM1hOWnZIN3RuWGFoN0NkYnZaY1NZT3ZaMHlOYmFhaVVxb253L01KQklhQzJMUTJUUXZYT1BBSWY0ZWJXSjdIMm1LL0gvMEVzY2Q0UjErWFEyZXB5ZVhHZkFxN3h3TS9TNTVHZ0NDSUtHMDdxem5Lemw0NGJjYlluOHBsbnhlNWUrYWJrL1o5SE9pNHFrRHE5a2RYekd1aWwrY1VsUGdwYWZoVndVcFptRUN1OFVzL1MzT29pYjJoT1dJdUdqNlJadThOa3RyanIrMGZ6TjJtdmNtKzhpZWxteHZrNFR3cEpvdXRxa29oN2pXRXFzYVdpaWt6NDRGTlE4eUMvRllBMnB3TzF0K3UwRTFzd2NTZFJyNG42T0t0eWQ4QlpoSXhnUHFIT2twZlQ1MGI5a0l3MExlQU52TW1PdVpjd0NFM3ZWNXg1aFh1bEhqeTR0VkhmeWlkSjBKdGxTaVk4NXk5cFYwZ0h2S294a0FIMmx0Q2JkZm41bU5GZGtYMU5mSXcwVW1jQVozRWJLNTFwdm1UM2hkdmp2ZUZoZ3NDUFJhYWFhZFN5MzJoMTNhakIyRU1kS3EvN1BTNXFySkEycElHd3VsY0RmL1JCTHJOdUpVMmk4VnZlWlZvcW41Y2FYZzlaVzJ5VGNUSEh3dHRYSS9XbjUvU2NPbmtCMXkiLCJtYWMiOiIwYjYzMDEyZjE1MTY5YzVhODkyZDVmZWI5NWJlYjY5YjA1NzRhNjZmYWUzYTJkNzg3NjZjYTFhN2NlMjNmYjY5IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6InpxSmJ0QVI0dkxweUtxVUkvdWVvL1E9PSIsInZhbHVlIjoiRkhNOWJNL0MyUzZibDBFUjJGNHJMMS9XRkZDR1l3VXdRMXJnRW1pVmpSdGdTb2hEQjlIUk90SC8waW96Y09LeDNQeldXcVRqLzY3TmhnUnBXVE8wVlVzaWFXVlRrbSsrSzgwbFYvNk9qYTFZbWFGUUJHNWRSTkNxeER5VWtFTTg0Tm5Zd0pLQTN2TFZkaFRyemFxT0IxU2o2OEJHZTFYVm92L2ZKdnFBcHRCbmlOQ3l5bll4elhieGtjeUQwbDJGTURieWdDTUt2SitFbERBRldhS0EzKzBVTm5RY2lmZWgrRE92ek9XMGp2dFRmVS8yUkl1ZjIxZHBuNlJCdHZzMnN2SjJEenlDOVBsU2FVbjY0MkEvQWdtM1J4bE9qS0s2NG94enBrVkZTdG45ZjJtVU9oTGVjZGQ3K0NYUlVPdCtkSTE5ZklUL2NWWnQzQmNRY3dZUHFzV1dKVGFHWExDajZGanBTb2tJYnBSUG5sZkMwc3ZwOGtoNXo4UVNzZE9KVGNYRHY2L0NhaU1waVNYbVg4T2U1dkJ5czFRQm9uNXBmQUxJR0p3L1Z2T2cwNC9VTFJNdVFrUWRna2JJOXZHWWZ6SHVXY2hlbDQ1QmFrSi96bllHUTI3ZDh0YkFaOWVzZDFHek4rMUhmNldnRDFQREpLOVNTSUdxUm9DeEFHU3QiLCJtYWMiOiI4MTAyZTI3ODk0MDQ1NzE2N2Y3ODkwNmY2N2QzOTdmY2FkZGRmYmZiNzIzNWQyYzZkYzNiM2U4NzJkNDE2MmUxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-133824615 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1XDrxTWmYv3gorVrXvmSodhuVHNiE4Wue14QMI4v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-133824615\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-364092039 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 07:19:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InZyQ3R6OU51V2FJblFzU0pMSnJtdVE9PSIsInZhbHVlIjoiMk9FaGk4TitCOHhOb0dRMElnaXVybTNkY05MMzBNVGEwZmE3Q2dvWVZVN05uTlQ0UVBXenZrd1VnMXdZRi9CMXhzY2o3bmFVMGxhcndNdThjbE5HL0dZclZlcWVKQWVLeTU5dzZKeDJ0OTFVZTVTVXdBcnBvdzdwU0I0MExmM1d5SGdhMEZibE1VZkh2d1hxRVhxQzdjaW15eTM1bm9BZmRPdVhXNmVVVUdLVGJQRlVod21mSFhBcjlac3ZEVlJUS0tkNlVBeHNnYnFDNHR4ZXh5THlEdFlyYW81bCtiUVNPaERMa3l0bmtXRWxXQnpSWXhrSmNVd1lVK3JyUWFTeFk0OXVmQnM3bE54V2RVSms5d05hNm40RU9ua0kwc2Ixakc5THlIcFZzL291bTJMUGpaT2Rpd0RtQ3BoNGRTT2h1OTRPdlBtenM0M1JyekpKZGpTZFVkcitIcEhNQVV5aHQvYXNsUXFnUFVUZVhFeWhuOWRXc044MllCNWQvMHk3bmI1MHI2c1Z1MFp1VXRDVTdzOTgzVlRNenl0NzdhN3UxTWJLUjBYV3ExTVZBcm5BYS9xVm1xaEpkRjBZMnpJcngralNLc2pmWlp6dWlHT2JrVldTb3FrYlBCak44SDdYakdBSEdkMzFqWVpXU1U4NlgwYVcxRDBtcEVUNCtTYmIiLCJtYWMiOiJkNzBiNmI3ZWNhNjAwZDE5ZjM2OTQ4YWViOTA5ZjliN2ZiNTM3ZThmZGE5MzljOTA5ZjcwMGRjYTcwNmNjNjBiIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 09:19:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InlQUVhLN3FkbjFQVDZCUG5WYUhQQ3c9PSIsInZhbHVlIjoiNEpWTUFyWlBicThJOFVOYXk1Ti9xbysrbVZ0ZnZjMGtTWmVXemVjNWF1Q2d1ZllwRGJBR0hmSHdkbE8vYzl5NmhCeDY3dFNPZDNEbGRqbklHZFpTdTBnNkd6MzNsWlB0VjFhdmJELzlNZ3I5bmY0eFNvcDRLZWxRdnRGNk40QUpyK3FtQy9CLyszM0xtZlc3cmthb0RYSWNMUDRqa2gzd3JNcTJuWGNJWGYwaDh4b2VIV1ZVdWV3elBIOEpSaHVzQ1h0bCtycUNQNkw5RW1KbFBXSnBud1Rqb01UQ1V1a2pEaHpKb0wzWDhWeGtUQzRVS3poMXdPYll6MExEVkFzVGNKLzZHL1ZqN1JHY0w4L0dTbmJod2I4NnNyUmZmakhmaVU3aUtKRDdXTmc2aDdzMTAwNGNuZ2tZbmNPZmJvVUt2U2RiODZmSzRsYkJZYjNOT1pRZXBRT0NXT0FKVXhuMzdFaHMrVWtrVkZlTjVtanhCb2xmWldwNDU3djdNb2tXSDhMTi9PTlZZcFhoN2tDSVlNUEZ2VEthZTVzZUJzOVJjNmdJN0RVTys0Mm83ajdDOWtsN3RYTkVyVFJBMmUyZVh6bm9Denh3UkFzb3R6b0RsU2I1YUV0OVJEZGpoTzF0RDNUUmNZQjlXRFpjSW9ycmdrU2p2alVZNWdvRHg4VlYiLCJtYWMiOiJhMGE4YTQ0ZGVjZWQ5ZDdhYjkxZTA5YWE4YWJhYzkzMDY4YjEzZWUxMzIzYTUyYjg4NTdiNWQzZTVhNmY0YTM4IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 09:19:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InZyQ3R6OU51V2FJblFzU0pMSnJtdVE9PSIsInZhbHVlIjoiMk9FaGk4TitCOHhOb0dRMElnaXVybTNkY05MMzBNVGEwZmE3Q2dvWVZVN05uTlQ0UVBXenZrd1VnMXdZRi9CMXhzY2o3bmFVMGxhcndNdThjbE5HL0dZclZlcWVKQWVLeTU5dzZKeDJ0OTFVZTVTVXdBcnBvdzdwU0I0MExmM1d5SGdhMEZibE1VZkh2d1hxRVhxQzdjaW15eTM1bm9BZmRPdVhXNmVVVUdLVGJQRlVod21mSFhBcjlac3ZEVlJUS0tkNlVBeHNnYnFDNHR4ZXh5THlEdFlyYW81bCtiUVNPaERMa3l0bmtXRWxXQnpSWXhrSmNVd1lVK3JyUWFTeFk0OXVmQnM3bE54V2RVSms5d05hNm40RU9ua0kwc2Ixakc5THlIcFZzL291bTJMUGpaT2Rpd0RtQ3BoNGRTT2h1OTRPdlBtenM0M1JyekpKZGpTZFVkcitIcEhNQVV5aHQvYXNsUXFnUFVUZVhFeWhuOWRXc044MllCNWQvMHk3bmI1MHI2c1Z1MFp1VXRDVTdzOTgzVlRNenl0NzdhN3UxTWJLUjBYV3ExTVZBcm5BYS9xVm1xaEpkRjBZMnpJcngralNLc2pmWlp6dWlHT2JrVldTb3FrYlBCak44SDdYakdBSEdkMzFqWVpXU1U4NlgwYVcxRDBtcEVUNCtTYmIiLCJtYWMiOiJkNzBiNmI3ZWNhNjAwZDE5ZjM2OTQ4YWViOTA5ZjliN2ZiNTM3ZThmZGE5MzljOTA5ZjcwMGRjYTcwNmNjNjBiIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 09:19:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InlQUVhLN3FkbjFQVDZCUG5WYUhQQ3c9PSIsInZhbHVlIjoiNEpWTUFyWlBicThJOFVOYXk1Ti9xbysrbVZ0ZnZjMGtTWmVXemVjNWF1Q2d1ZllwRGJBR0hmSHdkbE8vYzl5NmhCeDY3dFNPZDNEbGRqbklHZFpTdTBnNkd6MzNsWlB0VjFhdmJELzlNZ3I5bmY0eFNvcDRLZWxRdnRGNk40QUpyK3FtQy9CLyszM0xtZlc3cmthb0RYSWNMUDRqa2gzd3JNcTJuWGNJWGYwaDh4b2VIV1ZVdWV3elBIOEpSaHVzQ1h0bCtycUNQNkw5RW1KbFBXSnBud1Rqb01UQ1V1a2pEaHpKb0wzWDhWeGtUQzRVS3poMXdPYll6MExEVkFzVGNKLzZHL1ZqN1JHY0w4L0dTbmJod2I4NnNyUmZmakhmaVU3aUtKRDdXTmc2aDdzMTAwNGNuZ2tZbmNPZmJvVUt2U2RiODZmSzRsYkJZYjNOT1pRZXBRT0NXT0FKVXhuMzdFaHMrVWtrVkZlTjVtanhCb2xmWldwNDU3djdNb2tXSDhMTi9PTlZZcFhoN2tDSVlNUEZ2VEthZTVzZUJzOVJjNmdJN0RVTys0Mm83ajdDOWtsN3RYTkVyVFJBMmUyZVh6bm9Denh3UkFzb3R6b0RsU2I1YUV0OVJEZGpoTzF0RDNUUmNZQjlXRFpjSW9ycmdrU2p2alVZNWdvRHg4VlYiLCJtYWMiOiJhMGE4YTQ0ZGVjZWQ5ZDdhYjkxZTA5YWE4YWJhYzkzMDY4YjEzZWUxMzIzYTUyYjg4NTdiNWQzZTVhNmY0YTM4IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 09:19:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-364092039\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}