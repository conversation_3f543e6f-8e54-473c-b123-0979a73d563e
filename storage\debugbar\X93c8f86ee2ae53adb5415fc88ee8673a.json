{"__meta": {"id": "X93c8f86ee2ae53adb5415fc88ee8673a", "datetime": "2025-07-29 06:37:14", "utime": **********.103026, "method": "GET", "uri": "/api/leads/21", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753771032.918102, "end": **********.103065, "duration": 1.1849629878997803, "duration_str": "1.18s", "measures": [{"label": "Booting", "start": 1753771032.918102, "relative_start": 0, "end": 1753771033.967609, "relative_end": 1753771033.967609, "duration": 1.0495069026947021, "duration_str": "1.05s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753771033.967623, "relative_start": 1.0495209693908691, "end": **********.103068, "relative_end": 3.0994415283203125e-06, "duration": 0.13544511795043945, "duration_str": "135ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46081216, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/leads/{id}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ContactController@getLeadForPreview", "namespace": null, "prefix": "", "where": [], "as": "api.leads.show", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=531\" onclick=\"\">app/Http/Controllers/ContactController.php:531-567</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.02937, "accumulated_duration_str": "29.37ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.025664, "duration": 0.02616, "duration_str": "26.16ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 0, "width_percent": 89.07}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.068147, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 89.07, "width_percent": 3.235}, {"sql": "select * from `leads` where `id` = '21' and `created_by` = 84 limit 1", "type": "query", "params": [], "bindings": ["21", "84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 537}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0744832, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:537", "source": "app/Http/Controllers/ContactController.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=537", "ajax": false, "filename": "ContactController.php", "line": "537"}, "connection": "omx_sass_systam_db", "start_percent": 92.305, "width_percent": 2.86}, {"sql": "select * from `lead_stages` where `lead_stages`.`id` in (118)", "type": "query", "params": [], "bindings": ["118"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 537}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.082369, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:537", "source": "app/Http/Controllers/ContactController.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=537", "ajax": false, "filename": "ContactController.php", "line": "537"}, "connection": "omx_sass_systam_db", "start_percent": 95.165, "width_percent": 2.349}, {"sql": "select * from `pipelines` where `pipelines`.`id` in (30)", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 537}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.085943, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:537", "source": "app/Http/Controllers/ContactController.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=537", "ajax": false, "filename": "ContactController.php", "line": "537"}, "connection": "omx_sass_systam_db", "start_percent": 97.514, "width_percent": 2.486}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}, "App\\Models\\LeadStage": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLeadStage.php&line=1", "ajax": false, "filename": "LeadStage.php", "line": "?"}}, "App\\Models\\Pipeline": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPipeline.php&line=1", "ajax": false, "filename": "Pipeline.php", "line": "?"}}}, "count": 4, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/contacts\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/api/leads/21", "status_code": "<pre class=sf-dump id=sf-dump-1410945758 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1410945758\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-426643383 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-426643383\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-134360472 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-134360472\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-261649141 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IjB2SHZmTEJLbzdkOGtzZFdqWXpwL0E9PSIsInZhbHVlIjoiYkVyd0xEZmZ5REUwUm4xdTh2L1l4dktCdzRlb3p4N3B0ZXFPV3dwUDdkVVFWeFVRdEhzbmswSUROcDNIWWMvbDQrSnVkbVBXdHBieFNjUEU5SHZab0xhbFVGdWlOcEF2bkh2cFFsdFJaOGpHTmcwVXh1S2l0ZGlJeUJ4Zkx2c3k4enNYd0ZhaURwbE1MY0NEOWpaaVJqU2x3cEZCUENOMmNYcm1zVDQ3UUc3M0w1OEUxSFFOeEtNelZTY2oyb25GNlRzUUlPUldQRnlkQlAyWEF2OHVFUWo1NGhJcklyU3lMRVU0WGlVZ01yS1pZZzMwVzNVZjl1ZExSNEI2SVlRa3dhRW1qbjF2aXdzTlJLNWJaUU16aDhvakgyMVpZMkFOeGhRdjZXYk5abkF5RCt4RW5neGdEK0tya2NLZjRJYVZzekthMjB0azRva2ZxZVpQaUtXcnp6UU5sUGh5Rlo1ekR6bkI5QW9mQnRZVjF5VWw1eWVJb256RE9hdVVsLzdnZG9TdkErWjgzNStqclQrWDBlMDVVelQ0a05NR3dzdG9iWjNTSTYxdjZSTGdNQXhFVDlDZFhIbVdMWEQ2eEpYd1JZYTBYeVZlWHg5d3NEbmhJVGdxakwySklCbDdCVUtEU2lubUNUTCtmRWoxL2NuT2ZVRGJ4NndMRUFXc0EzbUoiLCJtYWMiOiJjNjBjZjQwYzZmNDhmMTBiNDJkZTRiOWUxMGJhN2Q2MmRlZmE4MWU4ZTQ0MTVkNzY4NGU3MTdlZjY3Njg1NTA4IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IjJhZmd5alpBOE9GMUp0dnFjMktsaVE9PSIsInZhbHVlIjoiMzROdUpWN203cU9KMzRZSUJkMFRNazdKZE9Fb0o1YUpUbklaSjFZUEtVV0JVOGsvbXQ1VGl3c0VxdkhyaHE4cStIR2VpYUFBU0xhMHJSS3BSdEwwcjNZV3g5VXptdHJmZDdLcldKZTl0SUQrTHFEYWhoY3p0YWdsdnRIR2hQc1JldTRVZUZ1OFp4K2pQMTRyejNlZEFsOGQxa3VBaWJIdEJ3TXprYzFlTCt0M3NwRTQ4c01ieTh2cW5CSi80T1hmUmcxU3AyWmlNRzRtWW1FSWRaamo3Z2djREErV3kzSnpwNTFpTHNhcit3L3d5THpHbE1TWXdudHdMcUxkNGtMLzVBN0xBT1RZQXFwdEw1cW5rQmtRMVJ0aHRUMHlaTXh4M0J1bUprb2tlS1RVYnptNFNBaG1TR24vNldQTTlHWGo4Kzc4OGlERjdxRFcrV2gzSStVSWFDdjBIcnp0ZndjMFdUYTgrQkRsVDVTREptN0E5WkJZMkJrVHhZd3JBeklaVVhJdkhycGJzbHpkcElHOXNuNkJKYlBicmZpKzRwQ2VjbkVqb0NVWmY1eklmODlEbVNGV2RHZTJKNE9XSlQ4dGpuSFRFN0dRWVYzd2lkOUlFS0ZDNVpxWW54MURNQXZ2QzNUMW9kb0JSYUNXWmducE1seGlIMkNEN3pjZ1hhWjAiLCJtYWMiOiI4ZWEzMWM0NDc5NjNhOTRkMTc4YTg2ZWU2OWIzMDAwMzRmZWNmMmY4MjcyM2E1ZTJjYjE0YzhlMjQ4NDM0ZmI5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-261649141\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1921127728 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1XDrxTWmYv3gorVrXvmSodhuVHNiE4Wue14QMI4v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1921127728\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1110107364 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 06:37:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkNFYk53NEJhS2RrbGZuWnZ0a2IwekE9PSIsInZhbHVlIjoiNHUxczJvUUZlaXFKeWFuSHFkdUNrZTMrd1k2Z21QVjlRd2FzYmlrSG1XYjZuN1BkVENFSkV4WjErdVZnaEN2Z1hFQ3dRQUdsOW96ZUplbFQrb1BTZElGWXZESGY5MmlBMlhaRWVQZ0lndXNTdXAyM3UwWmFUbHV6UlRnckdTYW1qVHdQeE1hVEdycGlDMUlaNlhRbXhoUll2NFZFajdOVFZqR2dvckNRWWVQcUtzVkxKT3ZSTzNXTG5ManBxNThmUHI5ck90dGJpb1prTW1JK1NhWjJkQlJVTWd1OUNGWlQyK2FKZkhLVTVVRkhsTzB5Q3VzeCtManVZcG9rdG85N1lFQWJEOWVkU1dtUFEzcG1sYW0wSFJvclRELytGcEhYSkJ6TDAvRWxTalg1UHZ5RWY5YzhrNkZ2SmVxOXJBT0NHNitjWmlhWGxVQkFWQVNDRzF4b1dlOENuTUhBcmpaR2pCNGw1TlBtNnI0NFpOa29PSjRiMXJLWlliM2Y2UjJtU2Y4OEQvZ25nNC80Ujk4Uy9KZTlHaktZQjZiekNUdW9HQW93d1ZHc0hhN1BVYmQrZk04V0dsdzlZL2pHbjBaYjc3TGd2QUxLaUpuM09aSUkxZnByS1JxVGZNaStzM0dDbVgrdlZ0UGRrQXBIT3JhamZERllIYnhWQjlVa2FyT2MiLCJtYWMiOiI0NzVhNTlhOTNkZjUzOThhYzc3MTFlOWQ1YTQ4ZGM0OTUwYTY0NGIyNTcyMzE0YTlmN2VmNjFlOGFkNmY4MDQwIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:37:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjZ6a2JZeWMwenNvV0o4VDRuQkc3dmc9PSIsInZhbHVlIjoiM0Rha1RBU0I4SCtsZjBSdU9SNXZjWnlZOXFyN0g3V1FHY3J2MngvYnF1V3pCS3BMc0toZm5icTFUcksrWndVTUdGNEtCSGdaWWVUY0tiaTB0NVozRXpzTm1sVGx5b1NYM3hrcjJMSmVTV1kwOW1aenljYkFSQWM1SVo3bXZUMnNseXNIT3JSMklUYU9Bb3VjY2cvelcxWnhJbTk4MVpNdHp4cGNLRjBYVHZaTXhiTzlWYm9xMTljRVZUL0g5OTJBR2xDNXBRNHRSWHRIeCtFZWs1RkZRQWluMjJ6STFRWFR4OGRhS3Z3aUFjVyt6WDExT3FRVCtYUThoTTM1UlgrWDVsUGVuckt3T2c5b3c3K2NpMzQ5MXBpRFQ2YmZ6cmRGdzlQZlpabWtjYWx3VHRDM1ZhYnpVWWk2VTdjVWtNUmFjZUhHYUlPc2FjQnY4UkpwWDJHa3FjeXhYNXl4OWwxMW1RU0FSYXpkbEZUaXRBdUo1cUNmR3EwWnJuL2lJUm80cE1vMk9YdXBvTmVNT3RuQnlqU3Npb1ZzTTFMWVN2YnNIRnpNMzVZWURtL0hLMy84ODYrajZrYjF2WGU3ZXpock9NQ0NpL3ZTSTJDZUJoTGp4ZDZYaXZYWXFvM20rUk9zWG9qeEx2TVBJNHRVSkptSFRCYlp1UkJRVmZ2NTVBUlMiLCJtYWMiOiI1OGU4MmMzNWRiNDViMzAwZDE0ZjY3MDgyYjAzODM2NjJmNjU1MzEwZWU4YWRjZDJiNGM4MjVlMTAxY2Y1OGQ2IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:37:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkNFYk53NEJhS2RrbGZuWnZ0a2IwekE9PSIsInZhbHVlIjoiNHUxczJvUUZlaXFKeWFuSHFkdUNrZTMrd1k2Z21QVjlRd2FzYmlrSG1XYjZuN1BkVENFSkV4WjErdVZnaEN2Z1hFQ3dRQUdsOW96ZUplbFQrb1BTZElGWXZESGY5MmlBMlhaRWVQZ0lndXNTdXAyM3UwWmFUbHV6UlRnckdTYW1qVHdQeE1hVEdycGlDMUlaNlhRbXhoUll2NFZFajdOVFZqR2dvckNRWWVQcUtzVkxKT3ZSTzNXTG5ManBxNThmUHI5ck90dGJpb1prTW1JK1NhWjJkQlJVTWd1OUNGWlQyK2FKZkhLVTVVRkhsTzB5Q3VzeCtManVZcG9rdG85N1lFQWJEOWVkU1dtUFEzcG1sYW0wSFJvclRELytGcEhYSkJ6TDAvRWxTalg1UHZ5RWY5YzhrNkZ2SmVxOXJBT0NHNitjWmlhWGxVQkFWQVNDRzF4b1dlOENuTUhBcmpaR2pCNGw1TlBtNnI0NFpOa29PSjRiMXJLWlliM2Y2UjJtU2Y4OEQvZ25nNC80Ujk4Uy9KZTlHaktZQjZiekNUdW9HQW93d1ZHc0hhN1BVYmQrZk04V0dsdzlZL2pHbjBaYjc3TGd2QUxLaUpuM09aSUkxZnByS1JxVGZNaStzM0dDbVgrdlZ0UGRrQXBIT3JhamZERllIYnhWQjlVa2FyT2MiLCJtYWMiOiI0NzVhNTlhOTNkZjUzOThhYzc3MTFlOWQ1YTQ4ZGM0OTUwYTY0NGIyNTcyMzE0YTlmN2VmNjFlOGFkNmY4MDQwIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:37:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjZ6a2JZeWMwenNvV0o4VDRuQkc3dmc9PSIsInZhbHVlIjoiM0Rha1RBU0I4SCtsZjBSdU9SNXZjWnlZOXFyN0g3V1FHY3J2MngvYnF1V3pCS3BMc0toZm5icTFUcksrWndVTUdGNEtCSGdaWWVUY0tiaTB0NVozRXpzTm1sVGx5b1NYM3hrcjJMSmVTV1kwOW1aenljYkFSQWM1SVo3bXZUMnNseXNIT3JSMklUYU9Bb3VjY2cvelcxWnhJbTk4MVpNdHp4cGNLRjBYVHZaTXhiTzlWYm9xMTljRVZUL0g5OTJBR2xDNXBRNHRSWHRIeCtFZWs1RkZRQWluMjJ6STFRWFR4OGRhS3Z3aUFjVyt6WDExT3FRVCtYUThoTTM1UlgrWDVsUGVuckt3T2c5b3c3K2NpMzQ5MXBpRFQ2YmZ6cmRGdzlQZlpabWtjYWx3VHRDM1ZhYnpVWWk2VTdjVWtNUmFjZUhHYUlPc2FjQnY4UkpwWDJHa3FjeXhYNXl4OWwxMW1RU0FSYXpkbEZUaXRBdUo1cUNmR3EwWnJuL2lJUm80cE1vMk9YdXBvTmVNT3RuQnlqU3Npb1ZzTTFMWVN2YnNIRnpNMzVZWURtL0hLMy84ODYrajZrYjF2WGU3ZXpock9NQ0NpL3ZTSTJDZUJoTGp4ZDZYaXZYWXFvM20rUk9zWG9qeEx2TVBJNHRVSkptSFRCYlp1UkJRVmZ2NTVBUlMiLCJtYWMiOiI1OGU4MmMzNWRiNDViMzAwZDE0ZjY3MDgyYjAzODM2NjJmNjU1MzEwZWU4YWRjZDJiNGM4MjVlMTAxY2Y1OGQ2IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:37:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1110107364\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1925084120 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1925084120\", {\"maxDepth\":0})</script>\n"}}