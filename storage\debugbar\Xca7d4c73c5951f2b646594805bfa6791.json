{"__meta": {"id": "Xca7d4c73c5951f2b646594805bfa6791", "datetime": "2025-07-29 06:37:54", "utime": **********.764948, "method": "POST", "uri": "/chats/favorites", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[06:37:54] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 84,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/favorites\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.758886, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753771073.552095, "end": **********.765014, "duration": 1.212918996810913, "duration_str": "1.21s", "measures": [{"label": "Booting", "start": 1753771073.552095, "relative_start": 0, "end": **********.538198, "relative_end": **********.538198, "duration": 0.9861030578613281, "duration_str": "986ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.538246, "relative_start": 0.9861509799957275, "end": **********.76502, "relative_end": 5.9604644775390625e-06, "duration": 0.22677397727966309, "duration_str": "227ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48268256, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.01464, "accumulated_duration_str": "14.64ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.610388, "duration": 0.00508, "duration_str": "5.08ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 0, "width_percent": 34.699}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.637238, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 34.699, "width_percent": 7.104}, {"sql": "select count(*) as aggregate from `ch_favorites` where `user_id` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 458}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.645551, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:458", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:458", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=458", "ajax": false, "filename": "MessagesController.php", "line": "458"}, "connection": "omx_sass_systam_db", "start_percent": 41.803, "width_percent": 8.333}, {"sql": "select * from `ch_favorites` where `user_id` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 459}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.651844, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:459", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:459", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=459", "ajax": false, "filename": "MessagesController.php", "line": "459"}, "connection": "omx_sass_systam_db", "start_percent": 50.137, "width_percent": 4.918}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.671684, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "omx_sass_systam_db", "start_percent": 55.055, "width_percent": 6.148}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (84) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.684942, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "omx_sass_systam_db", "start_percent": 61.202, "width_percent": 5.601}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (84) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.690192, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "omx_sass_systam_db", "start_percent": 66.803, "width_percent": 6.284}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.697713, "duration": 0.00394, "duration_str": "3.94ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "omx_sass_systam_db", "start_percent": 73.087, "width_percent": 26.913}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 543, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 546, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/contacts\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-2102280166 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2102280166\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1143125740 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1143125740\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-719472279 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-719472279\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1186227742 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6Ik50MXJTMmNzcThqTi94VnRRWkR4VUE9PSIsInZhbHVlIjoiN2wxMnZNY0Y0cUp2OHhqcFRDQlJPT3ZZS0dQQlBmdDVZMkJ3WG1IV2VQbU5VeWJkcmhzV2FvWXRpb0RnVGdSQjBHNXR3NmVlVTRKWUI1bjUzUkRLdzlBcXJhUk9ueVpxUVc3dUt4QklkTElyb2ZKN0lmeENFUTlGcW83QVp6aWRjSFpiZW5oa2xieXU4WXpOdmpDeDl4WHF6dmcwU2FqUGxIemdSWWxjMWF6ak9lMTcwbEdlRDI0QTNzellPdkNxbDU1a2o4eXRQYWM0T0hJektkWWZmTkczWDBFZS9TYUwzMWxOTTFzUzRGR1BBRGVISDhvVENraUNiMTg1KzNQSldwUjdBeWRJN3VkU1hwd2ZRWjczcXp3T1FmOUhpYUxjeW9vNmRoY3dzSTNxUGVrNTc4aXRZL3FuV0NsOGR2Y3dscDhEK000Rk44eU9oV1Zacmw2c0VrcFEvN1BSUjRpQ0JwcUh3U25NSjdYRks2MmIvNUlpTWhrM3h6OGk5OGwwUHFtTW90SW5NQTgwcXppZnQ0U2lDcEFBdXlXQzRsN0pMT2hqdzhlWVNEazVEQzdNL0hTbmhDQmRYODE0QXBLRFREUU1qQThrRFBWNHRmdUZ3Z3prMzBNd3IrYWxvWmJWT1pmY1QxS2NvRk5mVjhPbmk5MzJVVVRiNnRDVFlhS0ciLCJtYWMiOiI5Yjg1NmMxNDNmMzUxMzEyOThiZTRlNmJmNTVkYzhiMGMzOTQ1ZjM0MGQ2YjM4OWM4NmI5YTgxMDkyZTZjNzA4IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Ik1XWTZMZmk5U093R1dLVnM5ZFF3M0E9PSIsInZhbHVlIjoibTE4L2hjRW5Yc1d2OUExNFViQVpHendUWkt2ZHN5ZEhuK3pXSi9aSUxia2tzZ0VXUkJpazJub0JBT1ZwdlpPdFBkSGZsWUY3VlY1blZIQzNnU0M5Y1RlR0xQNDlPYUhOQ2J2NUxvR0FUTkEvUnJOeTFjMEo2M1R2WWxCWGdVNTR2b1JVL08vaUl0c3J3d1hzNnZnRktiaWNvYWV3WEVWVTNiU1dqdURZaU1WMFpKeGNNdC80L05WdGFzYnpBNXppam1WSk45ejVRemlBa01xSEt1eVVwNEhmREk3YnBtejNqVTFLbUUrTWR2VEE2K2JxdGZKblpENFk2V1JvVzhraWttOUtqWm9EZGhNZElKL2N2L0owQmswWW9HTW1ycnJrQjhtNTJwbjFadU9vQjdiK3krdGU1MGE5dHhhU2JxaHROVUVBeWtSQys4Z1U2WUdXYnVwUzMzT3p3MVdlUDdMRi9XeDNoUlYvcnNSaUZmKzB4SlFFUjlKZzJCNGQ3NmgreUdQNTJBLzl2WDlQY0J5OEs2RTg2L000aWhMZTJHVE45Q2Q5akx6QUZ2d0FjUUFpWEh3Skw4QTVMNWFGVHpOUDNnQ1lWUXZ4enBCZ2ZKMDRxL3J3SXlBNzk1QklhN1RqSEk4c3FtZWxxeFJ4dWFtZGJZMjZlN3BwWERvd0l6NXQiLCJtYWMiOiI3MDJkNjgyZmRlOTMxZjM2ZTIxNmZjZWUwZmNiN2YyODRjZTZmMGNmMjgwZGNjZjUyM2MzOTllYWE5ZGRiMTE4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1186227742\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-514497408 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1XDrxTWmYv3gorVrXvmSodhuVHNiE4Wue14QMI4v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-514497408\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-580398777 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 06:37:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik0zYTZ5ODNGT3gyZlg0WnEvVTZPb3c9PSIsInZhbHVlIjoiWVlRcHNjd0h0U3JDU0ZVRDN3UnVGUXdrdDE2b0pkRUppYkpBYTd5THIzVzBGeGxsaExZOXlISEgyaTFVNEpPSGtNVnlDTENtZWkvV0JtZGQ2cWxDWmJZa1NLYlF5NG43ZUJub0NpeitsY044UzV5WFNOZWpFR3k3YzlVZDR0VitzYlFQaFJkdGMvZXhVczlaOWdJSnBWUytiRmdHL1NiUGJyRXJmOUZhV2c5b3V3eWtjQld6Z3N0NW1LeU5pd3VOaDJZV3RFSGJaYkdUKzNGU0Zpay84Y29xc3pVcnJhVXFLY0NlcFZxemVaVzdacFZNYnpLMEZsZGNCTGNUN1F6TnEyWjUxNWZadmdaaDhZc1N5VnpwYzFRMkE4emUyYUhrcG8zcVdwemQ4bUZ5R0tMNy9pekRac21uMWRGMmYwK2lPa09adGxuM05YcnFabVVYbkxPQkI2RGZUUnMzem91YWt6aC95eVVxMzhwU1NIa3pNa1ZRMHlHTnZ5RWNVcm5ldW4rMGlEUTkwU0duRzIwcjA3cE42d3pzalhjekNFcTdRNjhBS05XbmhWV0ZCUU53MDd3M3paQmc3aU1ndTNTcS9KYlVoczAwUGVORFpLcHE5UzdxUjg0TGJpNXp1eFl4WWFuRGJvWmQ1aWVKYlY1ZDVuTjNONDdoNVF6VzVsalciLCJtYWMiOiI2ZmQ2NzE5OTdkMzUwZTRiNzJiYzA4N2NkYTBmODhmYzVlMWNmMTY0YWZiY2VkZTg5YmRiZjJjMmVkN2FiYmNmIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:37:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlpNaXJrQnNJdTNReER6djR2NjdaOFE9PSIsInZhbHVlIjoiT1N3c1BkRDFSTXdBTzZwOEc5VFdNV0EyaStUY0VXN0JJaWoxVG1nRGc0Syt6VGlEUFovLzR3bkt2Q2Z6Z1ZzL2drNlRPTFpKRTJ4eVp5U211VG9xVTdQRHhDanIwdkhTRit3bkRhWW1IeERZSWhjajlEdkpaNS9MY3NqTXB1OGFYNUptM0phTEdwYmdDWjVac3JKL1NZYm14QndNU3VDaVE2VWNtZjNjbjBLeFcyR21Sb0FkY2JuN1FwWHBVeHRNZG1xSDFpejZXTUh2K2h5VmJSTzUwN2ZDTEp6SWhrSWd1T0g4UmU0Z2tocEwwdnZaMGVlUXYvR2pKd29kWWZVUS9PZ0xGMnBQaXBTOFVKc1JjS1NlR3pFREk0dlBxM29GWXQ3czRCTUx4QVF6dmZYdzZxUU94eERUUEZ2L3d0d3Ayc00zQ2pzcFJSSXZhRFYwMmYySDd1Ymh2VWRZVVVTV2ZFdC9MbDR5QnV6UzhTdm1ET1A4a1ROUFhJbUhXMHFKQ0pncC9qOWFhbHY1WTd1cDJUcDZOUGZmYkdVcldqQ2RZYmZ4UWszaWgxN3JxWVdlbmdaWWdBenkwbnhKZmhvck5jWGpUR0ozcXpvTXVqd0RPNWJmcDhSMy9iUEZ3YThsMXJkbjgvVEdmYzhDVHd6VXE3bXJvRmt5MDFFZlpWUFkiLCJtYWMiOiI2MWFlNTdjMjIyODU2NGY4ZWU2M2Q2ZDljNjhhMjUxMmM3ODM3MjYzOTI5NGZlM2E0MmQwYmQxOTBhMWFjZTU5IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:37:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik0zYTZ5ODNGT3gyZlg0WnEvVTZPb3c9PSIsInZhbHVlIjoiWVlRcHNjd0h0U3JDU0ZVRDN3UnVGUXdrdDE2b0pkRUppYkpBYTd5THIzVzBGeGxsaExZOXlISEgyaTFVNEpPSGtNVnlDTENtZWkvV0JtZGQ2cWxDWmJZa1NLYlF5NG43ZUJub0NpeitsY044UzV5WFNOZWpFR3k3YzlVZDR0VitzYlFQaFJkdGMvZXhVczlaOWdJSnBWUytiRmdHL1NiUGJyRXJmOUZhV2c5b3V3eWtjQld6Z3N0NW1LeU5pd3VOaDJZV3RFSGJaYkdUKzNGU0Zpay84Y29xc3pVcnJhVXFLY0NlcFZxemVaVzdacFZNYnpLMEZsZGNCTGNUN1F6TnEyWjUxNWZadmdaaDhZc1N5VnpwYzFRMkE4emUyYUhrcG8zcVdwemQ4bUZ5R0tMNy9pekRac21uMWRGMmYwK2lPa09adGxuM05YcnFabVVYbkxPQkI2RGZUUnMzem91YWt6aC95eVVxMzhwU1NIa3pNa1ZRMHlHTnZ5RWNVcm5ldW4rMGlEUTkwU0duRzIwcjA3cE42d3pzalhjekNFcTdRNjhBS05XbmhWV0ZCUU53MDd3M3paQmc3aU1ndTNTcS9KYlVoczAwUGVORFpLcHE5UzdxUjg0TGJpNXp1eFl4WWFuRGJvWmQ1aWVKYlY1ZDVuTjNONDdoNVF6VzVsalciLCJtYWMiOiI2ZmQ2NzE5OTdkMzUwZTRiNzJiYzA4N2NkYTBmODhmYzVlMWNmMTY0YWZiY2VkZTg5YmRiZjJjMmVkN2FiYmNmIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:37:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlpNaXJrQnNJdTNReER6djR2NjdaOFE9PSIsInZhbHVlIjoiT1N3c1BkRDFSTXdBTzZwOEc5VFdNV0EyaStUY0VXN0JJaWoxVG1nRGc0Syt6VGlEUFovLzR3bkt2Q2Z6Z1ZzL2drNlRPTFpKRTJ4eVp5U211VG9xVTdQRHhDanIwdkhTRit3bkRhWW1IeERZSWhjajlEdkpaNS9MY3NqTXB1OGFYNUptM0phTEdwYmdDWjVac3JKL1NZYm14QndNU3VDaVE2VWNtZjNjbjBLeFcyR21Sb0FkY2JuN1FwWHBVeHRNZG1xSDFpejZXTUh2K2h5VmJSTzUwN2ZDTEp6SWhrSWd1T0g4UmU0Z2tocEwwdnZaMGVlUXYvR2pKd29kWWZVUS9PZ0xGMnBQaXBTOFVKc1JjS1NlR3pFREk0dlBxM29GWXQ3czRCTUx4QVF6dmZYdzZxUU94eERUUEZ2L3d0d3Ayc00zQ2pzcFJSSXZhRFYwMmYySDd1Ymh2VWRZVVVTV2ZFdC9MbDR5QnV6UzhTdm1ET1A4a1ROUFhJbUhXMHFKQ0pncC9qOWFhbHY1WTd1cDJUcDZOUGZmYkdVcldqQ2RZYmZ4UWszaWgxN3JxWVdlbmdaWWdBenkwbnhKZmhvck5jWGpUR0ozcXpvTXVqd0RPNWJmcDhSMy9iUEZ3YThsMXJkbjgvVEdmYzhDVHd6VXE3bXJvRmt5MDFFZlpWUFkiLCJtYWMiOiI2MWFlNTdjMjIyODU2NGY4ZWU2M2Q2ZDljNjhhMjUxMmM3ODM3MjYzOTI5NGZlM2E0MmQwYmQxOTBhMWFjZTU5IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:37:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-580398777\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1216038919 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1216038919\", {\"maxDepth\":0})</script>\n"}}