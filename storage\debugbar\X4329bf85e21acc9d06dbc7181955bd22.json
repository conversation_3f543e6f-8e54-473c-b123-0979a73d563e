{"__meta": {"id": "X4329bf85e21acc9d06dbc7181955bd22", "datetime": "2025-07-29 06:32:32", "utime": **********.807192, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753770751.697559, "end": **********.807251, "duration": 1.109691858291626, "duration_str": "1.11s", "measures": [{"label": "Booting", "start": 1753770751.697559, "relative_start": 0, "end": **********.713054, "relative_end": **********.713054, "duration": 1.0154948234558105, "duration_str": "1.02s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.713101, "relative_start": 1.****************, "end": **********.807254, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "94.15ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2999\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1805 to 1811\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1805\" onclick=\"\">routes/web.php:1805-1811</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "H7YzpgeQomM5xBO5yWSpzk3aF9zyjId5bNn86ycl", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-1654724259 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1654724259\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1048597277 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1048597277\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1095063033 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1095063033\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1154834105 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1154834105\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-316094940 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-316094940\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1540377052 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 06:32:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IitMSmRuc0lPQ3lwc3RtN2E0WWx6Vnc9PSIsInZhbHVlIjoiY1JTbHZGNmZ1TDB4U0MvZG12SUVIQTR1VGlSeGdGUjFIUUIyMWJ2cUJpdHFlSnJpSVBBM0UzbEtyckZZY2FNbVlteGFNMkN5UDMzeUl0d29McEY0ekFlNDJ1cEVQazlza1dCSkJvd1VoUHh0SkVKVjFBRWVueW9oSlgrcHhUNTBKTVUwVHhMSHpHV1pIeG5CejhiSis4MldUbW02dEROdEV6V0RMZGw4M01TdXN1UXFvYUswcHArOTRkVmwyVHRzclJXdWkzNzhXVmFmcFdwS2tWUjBCZGxNMW00UEhsRDA5anhXbmoxSWdJeExhWkFUVTVta3hFbU81SXpNSmtadExpRHdydFdiY2N2SXNaTFA2NUJDZDFwWWREanIxQm5KRThoaGZnSVFtVWR1cVFRdWV4TTMxRXNPbDVmOVhCTEhMUC8rOWJLWEhOZ3lXWCtXcmdxRUVRb1Qrd1lTMXB3cTRmbW9lOGV6bURaMTc2Q2FKa2FOUFZFaDF5ckcxc0w5WVV0NE1tazBuSzhyK0x4cUo1K09DYURqN0JjTncrOUlSZXJ3eEdmVkJUbzFiZlFRbFZxcDNMNW5JUGw0UGE5ZVBFRjMzN1hDQU0ySmVEUStqODlsRGhkWDNwZXhoNFV4V2ZNS2lzK2Nza3JEb1B4V3dUN2JRSnFWVW1BTUI0YXQiLCJtYWMiOiIxYzExYjE1ZTU5NGY3NDQ3NmQ1OTRjNzYzOTNkODM2Zjg3NjQ2NTJhYjIxMmJiZWFiZWNhYzQ2MmM3ZDQ4MDc2IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:32:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkUwUkh6K3YxTzBWS1I2Y0VWWXMrZVE9PSIsInZhbHVlIjoiZk1UUkRmbDlkOFZBMVdsRU1zSm92ZmRVaGpwWnlSWGVEZnpZcHIvMzVtbmFLSEhxWnROblZLQjlCcHVaSVR6VjAzUTg5azRCdEFrM0p6SmY2L2l2M0Z1YWQ2Z25JNzVwaWZhVUlDSUh0SEZKTmdXSU8xZmRzTE5KZTRjU0FyOW1FdFNXc3AzTTY1MDVsYUxuc2FRU1VxSmtFWkx1RU9VdSthcll2WkliOWxKazY4Q2lTSGJpM29CY1JrQkJOZlZDK0R1TitvenJaRStjYUdQYTJvZG9KT2hpUklZY09lMmFsTVRhamtudHdtSU14WlU5QnNsaEpXeU5mUHlFbkRIeE90TDJkb0NKdmlKQ3k5clNCNUxac0J3SW1ieFlUVmVCM3o3M0IxZ3FZazNNZFU3V3hIQmtVbkdxR0RMS3Z6ODRZTmRISkFYWUJkbnBCbUhLMXVpK1FjYTN0RmhLd2RaeGFETDhMeUgzaGZ4UmF1WEovd0xPRWtVaURETS9wYmg3TUVhWnNWZUREaXgwUCthOCsyK0VqekR1OSt4MGc1eXFMaG5hSW10ckNxWmloSEQvcitCTTIyR2lVbjBxY0JtNVh4dm1LbjF1MWdsanYrb214OXJIT1cvdktwSERFQnhtV2FIeWNQVkpzTzlNMEtCU2pvWUZ3MnpnRHpuTGRDaTciLCJtYWMiOiJhZTJjN2QwM2ZjMjg5OTIzMWVjMTY0MDkzYzFiNDBlYmY5NWVmYTgxMGUyMTc2YzI1ZDUwYjBkMWZlZjkzZWQwIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:32:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IitMSmRuc0lPQ3lwc3RtN2E0WWx6Vnc9PSIsInZhbHVlIjoiY1JTbHZGNmZ1TDB4U0MvZG12SUVIQTR1VGlSeGdGUjFIUUIyMWJ2cUJpdHFlSnJpSVBBM0UzbEtyckZZY2FNbVlteGFNMkN5UDMzeUl0d29McEY0ekFlNDJ1cEVQazlza1dCSkJvd1VoUHh0SkVKVjFBRWVueW9oSlgrcHhUNTBKTVUwVHhMSHpHV1pIeG5CejhiSis4MldUbW02dEROdEV6V0RMZGw4M01TdXN1UXFvYUswcHArOTRkVmwyVHRzclJXdWkzNzhXVmFmcFdwS2tWUjBCZGxNMW00UEhsRDA5anhXbmoxSWdJeExhWkFUVTVta3hFbU81SXpNSmtadExpRHdydFdiY2N2SXNaTFA2NUJDZDFwWWREanIxQm5KRThoaGZnSVFtVWR1cVFRdWV4TTMxRXNPbDVmOVhCTEhMUC8rOWJLWEhOZ3lXWCtXcmdxRUVRb1Qrd1lTMXB3cTRmbW9lOGV6bURaMTc2Q2FKa2FOUFZFaDF5ckcxc0w5WVV0NE1tazBuSzhyK0x4cUo1K09DYURqN0JjTncrOUlSZXJ3eEdmVkJUbzFiZlFRbFZxcDNMNW5JUGw0UGE5ZVBFRjMzN1hDQU0ySmVEUStqODlsRGhkWDNwZXhoNFV4V2ZNS2lzK2Nza3JEb1B4V3dUN2JRSnFWVW1BTUI0YXQiLCJtYWMiOiIxYzExYjE1ZTU5NGY3NDQ3NmQ1OTRjNzYzOTNkODM2Zjg3NjQ2NTJhYjIxMmJiZWFiZWNhYzQ2MmM3ZDQ4MDc2IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:32:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkUwUkh6K3YxTzBWS1I2Y0VWWXMrZVE9PSIsInZhbHVlIjoiZk1UUkRmbDlkOFZBMVdsRU1zSm92ZmRVaGpwWnlSWGVEZnpZcHIvMzVtbmFLSEhxWnROblZLQjlCcHVaSVR6VjAzUTg5azRCdEFrM0p6SmY2L2l2M0Z1YWQ2Z25JNzVwaWZhVUlDSUh0SEZKTmdXSU8xZmRzTE5KZTRjU0FyOW1FdFNXc3AzTTY1MDVsYUxuc2FRU1VxSmtFWkx1RU9VdSthcll2WkliOWxKazY4Q2lTSGJpM29CY1JrQkJOZlZDK0R1TitvenJaRStjYUdQYTJvZG9KT2hpUklZY09lMmFsTVRhamtudHdtSU14WlU5QnNsaEpXeU5mUHlFbkRIeE90TDJkb0NKdmlKQ3k5clNCNUxac0J3SW1ieFlUVmVCM3o3M0IxZ3FZazNNZFU3V3hIQmtVbkdxR0RMS3Z6ODRZTmRISkFYWUJkbnBCbUhLMXVpK1FjYTN0RmhLd2RaeGFETDhMeUgzaGZ4UmF1WEovd0xPRWtVaURETS9wYmg3TUVhWnNWZUREaXgwUCthOCsyK0VqekR1OSt4MGc1eXFMaG5hSW10ckNxWmloSEQvcitCTTIyR2lVbjBxY0JtNVh4dm1LbjF1MWdsanYrb214OXJIT1cvdktwSERFQnhtV2FIeWNQVkpzTzlNMEtCU2pvWUZ3MnpnRHpuTGRDaTciLCJtYWMiOiJhZTJjN2QwM2ZjMjg5OTIzMWVjMTY0MDkzYzFiNDBlYmY5NWVmYTgxMGUyMTc2YzI1ZDUwYjBkMWZlZjkzZWQwIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:32:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1540377052\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-10957875 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">H7YzpgeQomM5xBO5yWSpzk3aF9zyjId5bNn86ycl</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-10957875\", {\"maxDepth\":0})</script>\n"}}