{"__meta": {"id": "Xecc1b27119e5cb2cacffd9b9907cf3a6", "datetime": "2025-07-29 06:33:18", "utime": **********.786567, "method": "GET", "uri": "/contact-groups/available-contacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753770797.782708, "end": **********.786585, "duration": 1.0038771629333496, "duration_str": "1s", "measures": [{"label": "Booting", "start": 1753770797.782708, "relative_start": 0, "end": **********.63512, "relative_end": **********.63512, "duration": 0.8524119853973389, "duration_str": "852ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.635157, "relative_start": 0.8524491786956787, "end": **********.786587, "relative_end": 1.9073486328125e-06, "duration": 0.1514298915863037, "duration_str": "151ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46658896, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET contact-groups/available-contacts", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\ContactGroupController@getAvailableContacts", "namespace": null, "prefix": "", "where": [], "as": "contact-groups.available-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactGroupController.php&line=221\" onclick=\"\">app/Http/Controllers/ContactGroupController.php:221-260</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01831, "accumulated_duration_str": "18.31ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7353249, "duration": 0.01252, "duration_str": "12.52ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 0, "width_percent": 68.378}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.7624319, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 68.378, "width_percent": 5.134}, {"sql": "select `id`, `name`, `email`, `phone` from `leads` where `created_by` = 84 and `contact_group_id` is null and `is_active` = 1 and `is_deleted` = 0 order by `name` asc", "type": "query", "params": [], "bindings": ["84", "1", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ContactGroupController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactGroupController.php", "line": 231}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.768255, "duration": 0.004849999999999999, "duration_str": "4.85ms", "memory": 0, "memory_str": null, "filename": "ContactGroupController.php:231", "source": "app/Http/Controllers/ContactGroupController.php:231", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactGroupController.php&line=231", "ajax": false, "filename": "ContactGroupController.php", "line": "231"}, "connection": "omx_sass_systam_db", "start_percent": 73.512, "width_percent": 26.488}]}, "models": {"data": {"App\\Models\\Lead": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/contact-groups\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/contact-groups/available-contacts", "status_code": "<pre class=sf-dump id=sf-dump-1226830059 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1226830059\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1184281936 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1184281936\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1015422906 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1015422906\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1946373908 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/contact-groups</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IlJmajJKNlA3Ryt1d2FNSmI4eGF3NWc9PSIsInZhbHVlIjoiWmVUbU9LcWVJMjE4TDFCUHJ5bXpEbFdkbHcrQk0xTlF2OVdhRHdiU0pTc3NHQUtLVmRrRFJ4RG1acnFNZDVrd3hXeU5WL1Z4TkRBazRxc3NLUklEek1JSGxlWVJOUXJjd29YeGpJYWtscW9sUHhwdWV2R2FxUi9ZWEhoT2ErNHdGd2plU0RJdzdrRGpmWTlSVjhacHczVFZNNlF2b2dzWUMvMVJsR3V2Zmt0cmVycVpLdnVPWW1KenN5QmVKbVJHd29oOEdFRjRRbkFYdi92YlF3OTNDNm9nWU5PeUFYR0NVa1FtYlJDakNuM0N5cyt3T2tQUy9aT2J1TlJ1Mm52Ykp5RlR1czhaUnkrMWZyZkplOVVCb05IMm5QWFhXZnBib1Y2U20yTXQ2ZzJQZ29MbFltR3pkTWNvYWpYU1orRk12SVRmRmdzNjk4M1pSamZkNE01aUtMcFlJUlYrTkJBdUFzQ2FkUXE2cEt2enAxcVZQUXJuOUY0WWJZS1hWWXVMRkZ2ZVZiTU5iaHc1bi9KUVVFOUdRUFRBT0tmZ3F0VzI1blJvWjBOSmwrWitSTUYvWXdPd1NUMENxOHhOaWlMVFpHTnU1eHJ1WWhvbHIzL1Rja1lKWkI3K2pqT3N3TVg3QjBSc2UyNGtwcXZsTG5aUGkveHZHMlo4SnBGQ1dmWGMiLCJtYWMiOiIyODdiNzYxODFjMmRmYjBlYzA4ZjJjNTlhNmNhM2YyYTY2ZWQxMDdlMDk0OTM0NGQ0NWEzNGM5Nzg1YjhlMjBmIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlIvY2ZLMGxxOEtlUFBOVnRCd0VmdlE9PSIsInZhbHVlIjoiczBqR0FsckJFZitDeUJNelhBdXZjdFBmcG5hMkhOWTgvelF0UG1Db0t6dXhUbytVQkJOTG9VMWl6NVQ2RE13VDJwWUpVMWNGQitoN0tLQkloSTZJbmdRbzR3MDdwTUZNV1ZLZFo0emZJTzJJcG5kUXY2RUo1Yk1rZlFoZndpVkxzTDk4SWQwNFBEYXdYd3RVM2pKNHMwWjVadjBIV2szUkRpUjZpbERYRU9rTm1pL1pJRDIwZVRxTzBDSWtTWmNubllNKzkxRTRYK3JSSGZ4UEdoWmNKd0VyUFc0cHQzcGZOU3l5RHlUYjRJNVkvVlFvYUIrN3M1U0R1K2RnSzM3TWd4d0phYlFNNkZDcEl3NFRBRXQxWjZTeExBSWJwOE13OXpMWklwVWYzcUhCVU4rakRiUEhwdEg0YjR4NFo0MWlmOGNhckp6L0ljUVlDbHVLOFE1aFA5NThjWUJ1c3hZNU4rSVplU3U3d3BkTDV6bFRkS3FJRzJkK3doQ3pQQ1k3NWFwemFoMUJwUkxXYng3VUZWem1vYU9DenpRZ3Y2UDVKaTV6NHU1MEpYa3JQQnJ2ZGJZYXNPN05lbDlTTWp6b1FmK3dmbnhPWjN2bjRsM1FmRnhPRFV1WkZiaU8wUTA4bE56VDR2SVl0a0Y5VUNHaW1sc0pFQ2JHNEx4TnVWZ04iLCJtYWMiOiI2OTExMzkwMzcwMmM2ZGIxOGY0M2ZlMzYyYmM4OWQ1ODkyN2RiNDg4NjUzODE0NTc3MDRlYWI0MTA1YWQ0NTE2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1946373908\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-575117502 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1XDrxTWmYv3gorVrXvmSodhuVHNiE4Wue14QMI4v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-575117502\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1558881414 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 06:33:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InRWVUlQcVozYWIvZFROSTgyZkZrcWc9PSIsInZhbHVlIjoiUjRRVHJhTXQxQlEwc0Q1NkVuN0dUL292U0dHbkhuNTBTemNsakdsaUthZnJIL2NVdU10eVJEMldXZTBzWjRUejM0WCtOR1BEN2Q0N0lIWUV5cnlSbDlUcUFyUndKbzc5VkxLOTZNQ3FtR1FhNDc4VlFPY3VwTm1qYWhxMndaM0oyM1k3bC84RGlZNDB1MjNrQkI1MTRyVmpJcEN1QitIc05DRmI3TGtMOEF1YnlEcE5wdzhDcVBlWmFVcm5ERklyWW1PTjNadnRURGJIY3QwbXFaWUxZZk54Z3AyOFBkejJWaXBjL2RkTWpUcG9jaU5NR2xmS2IxK0ZPMGZZTTZxRGl4QjltaGd6Ti83WmFiYkk2ZTJ0SEhORW02YVVIYjB2bmxZUzFaSVUySUFaWmVtTWNKNnA2b0J2a0w2czd3aU8wWCtaMVVSUGxNMUxpYUQ0YmFpQVVWcmx3VFhvRkdWTCsxbGRRcStNbmlUTjQ1K0ZKQVN0TlNQc0M3bDdycU9wNG5qcmJCYlpObEpMZGVFQUJSMXhiMVpQcTc0elBFN2k0cVRGeFlXY0Z6NjQ4VGNqOVdPV05weVVYUy9vaTZMSWZmZnJNZndiQ2VqODc1Uk54VTd3bmNFZDNEeTA2SFluNlFDMVhkakREdWgzY3JLTEhuTStycTdmODBCZ3h6Z1UiLCJtYWMiOiJkZjU1ODk4OWZlZmE0OWJkMzliZDEyY2ZjMTVhNDRkNmI4MjQwZmZkZjBjZTNjMjVlMjBjNmEwM2UyOTVhOTY4IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:33:18 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkxMWTV3WVB3WHhSWE1SblZ1WlpXU0E9PSIsInZhbHVlIjoiT0UyTjBoL0R1Y2c4Vk9Hbkl5MHF2Q2hSeW5ZL2IxdlhMb3IyMERhdU9xRHRoamg3QisrYVVJNjhjclNwY0VpRkxTbW84OEdaTUh2MHUzQ2JoQnJwMU1TSjljZWVXWCtwOGNRMFd4NERieTlGS0l3cXlMbXRFdGhUbjVIVFA4ZkpLU2JUWWcraWhKNmJWOWZWZ0dmRTFMU2FlZkxwY0VlV3NIVnhvYkc1UllnUW1Zb2MzbXJKRHNIL1AxNEoxcHkySld4ajR6ZHhpSGlKQ3NrNnV5YzFIRHJMVjg3dzFQejZYd0M5b2xvdThpczBuMUdLTmw0akx2NmdCRlBCOXJSTFo5c3Biall5OVBIWmQvcmlJTXF1ejREYzdOVXFXNGM1Y1lRSFVIMVNMMVBLSXFIQWpBZno2L0xrSTR1MnRQWXl5enNoZzY2RUkyNEZEWWdvNWZxb2FMK01VOWN6TWZOeTlhYXp2UlJseE1OZk85ckVLQ3JyTEFsdlJYS2l6NW5ERjBkYlZPcDJpNjMrSjEvUlVwMkc4S0FUSk43d0Noem9rMUVham54bzFscWoveHV2ckh4K1pVRlRFc09NMkx4akp5VFF4MzR2djNDMno4ejFoVkx0ZVR6aEVWUkZiRi9CTW5sR2lRdUdaelRFeml6NEttZGxqaGtFb1dqMzY0cUQiLCJtYWMiOiI1ZGU3MTczZTgwYWYxYWZkNTA5ZTkyOGI0OWI5OTEyMWQ4YzU0YTZlOGIxNzBjYjA0MzBkZmRjZTFlYmQ1ZjZhIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:33:18 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InRWVUlQcVozYWIvZFROSTgyZkZrcWc9PSIsInZhbHVlIjoiUjRRVHJhTXQxQlEwc0Q1NkVuN0dUL292U0dHbkhuNTBTemNsakdsaUthZnJIL2NVdU10eVJEMldXZTBzWjRUejM0WCtOR1BEN2Q0N0lIWUV5cnlSbDlUcUFyUndKbzc5VkxLOTZNQ3FtR1FhNDc4VlFPY3VwTm1qYWhxMndaM0oyM1k3bC84RGlZNDB1MjNrQkI1MTRyVmpJcEN1QitIc05DRmI3TGtMOEF1YnlEcE5wdzhDcVBlWmFVcm5ERklyWW1PTjNadnRURGJIY3QwbXFaWUxZZk54Z3AyOFBkejJWaXBjL2RkTWpUcG9jaU5NR2xmS2IxK0ZPMGZZTTZxRGl4QjltaGd6Ti83WmFiYkk2ZTJ0SEhORW02YVVIYjB2bmxZUzFaSVUySUFaWmVtTWNKNnA2b0J2a0w2czd3aU8wWCtaMVVSUGxNMUxpYUQ0YmFpQVVWcmx3VFhvRkdWTCsxbGRRcStNbmlUTjQ1K0ZKQVN0TlNQc0M3bDdycU9wNG5qcmJCYlpObEpMZGVFQUJSMXhiMVpQcTc0elBFN2k0cVRGeFlXY0Z6NjQ4VGNqOVdPV05weVVYUy9vaTZMSWZmZnJNZndiQ2VqODc1Uk54VTd3bmNFZDNEeTA2SFluNlFDMVhkakREdWgzY3JLTEhuTStycTdmODBCZ3h6Z1UiLCJtYWMiOiJkZjU1ODk4OWZlZmE0OWJkMzliZDEyY2ZjMTVhNDRkNmI4MjQwZmZkZjBjZTNjMjVlMjBjNmEwM2UyOTVhOTY4IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:33:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkxMWTV3WVB3WHhSWE1SblZ1WlpXU0E9PSIsInZhbHVlIjoiT0UyTjBoL0R1Y2c4Vk9Hbkl5MHF2Q2hSeW5ZL2IxdlhMb3IyMERhdU9xRHRoamg3QisrYVVJNjhjclNwY0VpRkxTbW84OEdaTUh2MHUzQ2JoQnJwMU1TSjljZWVXWCtwOGNRMFd4NERieTlGS0l3cXlMbXRFdGhUbjVIVFA4ZkpLU2JUWWcraWhKNmJWOWZWZ0dmRTFMU2FlZkxwY0VlV3NIVnhvYkc1UllnUW1Zb2MzbXJKRHNIL1AxNEoxcHkySld4ajR6ZHhpSGlKQ3NrNnV5YzFIRHJMVjg3dzFQejZYd0M5b2xvdThpczBuMUdLTmw0akx2NmdCRlBCOXJSTFo5c3Biall5OVBIWmQvcmlJTXF1ejREYzdOVXFXNGM1Y1lRSFVIMVNMMVBLSXFIQWpBZno2L0xrSTR1MnRQWXl5enNoZzY2RUkyNEZEWWdvNWZxb2FMK01VOWN6TWZOeTlhYXp2UlJseE1OZk85ckVLQ3JyTEFsdlJYS2l6NW5ERjBkYlZPcDJpNjMrSjEvUlVwMkc4S0FUSk43d0Noem9rMUVham54bzFscWoveHV2ckh4K1pVRlRFc09NMkx4akp5VFF4MzR2djNDMno4ejFoVkx0ZVR6aEVWUkZiRi9CTW5sR2lRdUdaelRFeml6NEttZGxqaGtFb1dqMzY0cUQiLCJtYWMiOiI1ZGU3MTczZTgwYWYxYWZkNTA5ZTkyOGI0OWI5OTEyMWQ4YzU0YTZlOGIxNzBjYjA0MzBkZmRjZTFlYmQ1ZjZhIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:33:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1558881414\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1706868247 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/contact-groups</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1706868247\", {\"maxDepth\":0})</script>\n"}}