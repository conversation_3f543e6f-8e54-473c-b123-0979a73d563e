{"__meta": {"id": "X3b649b1c5f2f182aa8751d40767a29ea", "datetime": "2025-07-29 06:33:06", "utime": **********.222847, "method": "GET", "uri": "/contact-groups/4/members", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753770785.159398, "end": **********.222866, "duration": 1.0634679794311523, "duration_str": "1.06s", "measures": [{"label": "Booting", "start": 1753770785.159398, "relative_start": 0, "end": **********.105383, "relative_end": **********.105383, "duration": 0.9459848403930664, "duration_str": "946ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.105396, "relative_start": 0.945997953414917, "end": **********.222868, "relative_end": 1.9073486328125e-06, "duration": 0.11747193336486816, "duration_str": "117ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46652136, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET contact-groups/{id}/members", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\ContactGroupController@getGroupMembers", "namespace": null, "prefix": "", "where": [], "as": "contact-groups.members", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactGroupController.php&line=328\" onclick=\"\">app/Http/Controllers/ContactGroupController.php:328-372</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.011389999999999999, "accumulated_duration_str": "11.39ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1764011, "duration": 0.00783, "duration_str": "7.83ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 0, "width_percent": 68.745}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.196671, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 68.745, "width_percent": 18.174}, {"sql": "select * from `contact_groups` where `id` = '4' and `created_by` = 84 limit 1", "type": "query", "params": [], "bindings": ["4", "84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ContactGroupController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactGroupController.php", "line": 333}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.203921, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "ContactGroupController.php:333", "source": "app/Http/Controllers/ContactGroupController.php:333", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactGroupController.php&line=333", "ajax": false, "filename": "ContactGroupController.php", "line": "333"}, "connection": "omx_sass_systam_db", "start_percent": 86.918, "width_percent": 6.234}, {"sql": "select * from `leads` where `contact_group_id` = '4'", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ContactGroupController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactGroupController.php", "line": 342}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.208536, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "ContactGroupController.php:342", "source": "app/Http/Controllers/ContactGroupController.php:342", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactGroupController.php&line=342", "ajax": false, "filename": "ContactGroupController.php", "line": "342"}, "connection": "omx_sass_systam_db", "start_percent": 93.152, "width_percent": 6.848}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ContactGroup": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FContactGroup.php&line=1", "ajax": false, "filename": "ContactGroup.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/contact-groups\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/contact-groups/4/members", "status_code": "<pre class=sf-dump id=sf-dump-1794444744 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1794444744\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-614004699 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-614004699\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-256342226 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-256342226\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-276530281 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/contact-groups</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6ImVwWWI3YXhMckY4eWQ4L3VpYzRzMmc9PSIsInZhbHVlIjoicTlsMEhydWtLVmFVWVUwcFlGTXFxU2ZPZlAxOXhnQWlCVGI1ZTNKVFNQWEhVekVLakIwcTBnN1REWWlSemZoV1BQeWNadzQzYXRTakxpVU9GVVFmYlRYd0EvQVBOcGljcWx0WE1MdkF6MlRLaFo2R0pWSUtWaFJrMDVDeUNRZVU5NGlOUlJSa2Rzemt5NjhxQkt2cHVqaEZLdnRpMnROTnFOS2IvcS92ZVZUM2tUTWtteVU2bmxqQWxmYTVXSFZOMHNMNS9jbXQ0WjNJb2FFYTRXQWV6ODBpWkErWTU4YXNtZWV1Vkd0UVZ3UzVRdDYvMGFVTG10S3VSRmlES3QrbDI3b1hRWE5CZXNqQitBL3A4c29mMlhmOElhWEFiNU5OZms0eGp4dThKYW04NGxKL3J4azM3dDhLS3V4WHZvMHhuRzJxWE1RUUNVblM1Y0MvbGRKTGVTWmFwN1NrcDZtNGhrbGNVMGlEVGdoUWNTY3RjVDV0SkIrcXZjSXM1NWhuSUM5M1JxOVE4YjNjVXVVV29DTVVZUmxDS1dtUGRodjUrT2dpYzQzVmFNOVIxeDJCSmF3ZnVxclEzVWY3aDB1MGhVVE93MC9WN21wY3FUK04rVW1kcDlWbmM5eFRVaUdDUlZaNmc0WEo0RklpWmI2aG1KdEZtZnRhR1Z4aERyQWwiLCJtYWMiOiIzM2QzYzE0MWI1MDcxZDM4NTY3OWNiYjNhMmNlZDQ4YzFjYzRhZGRmMDk4YWY5ZjZkMTJlNGFkYjhhYzJhOTk5IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6InpoakRyWXZrR0lVVEJDK2todXY1TkE9PSIsInZhbHVlIjoibkUxczY0T2ZjNjB0dW4rYURYWUcvWHB5NUlyekpVMlBWOE1JRjgxNFd1RHdvWC9tWnBId2prenl6eVJVaGRsVlBhVEdzMTVwMnN0WW1GM29vZEY1QkZPb2I3TjJNdVRhWjQyRFBYNGJGSmZIK2k5UzNkeWNFcmtJbENsRGJwNStrYkc0M3FJVVdmRFlKMnJtdHByYXhLdFphcnFyRjFkbjZPOTNnV0orTlJCdFVoREJGNFJTUmpXMUdycjd6YnBlVE4wT1JZSjlhOUUzMFZ0dmhxK2QvMHJxWVcrczlsNmljUjNrNGw3U1ZMdmxnWDFuSE1mV0NHWWtmS00wVy95c3E5MWd2UkVleUsyUkJWa0ZxUlJOcG52QkZYS25CaTc5YWRlY3NBOWRKUVpXeUhib2VoR2plMkJWcUc4Z1NCQU9BdzA3SnhyOVpBcEZZZ043RTdwcWV0UU1ielQ3RCtqWXVSNkVJSUt3am5aNmI3TU96WmM4NHdFSnZEa3EwNlZocjFua21rcTM3NDN2OGQ1b3NrckF0NVFQYUNmVFBKdy9JTGRoUU9xSVM1SGRHK2JOcnJlam9GNVBQcDRQS0hid1RRL256dXNsKzRDbm5TOUJIYnR6SkZDK1FuakZlckRpNEJPdjlFR2pVeUdrTmJiQmpGZTFYK3FyQzlhSXpyL3MiLCJtYWMiOiI4ZjdkNDkzNjc4ZTgwNzg4M2I4NDQ1MjcyZGE3MzcwMWUyY2UzOWExZGUyYTYzMmZiNWJkOGIyNjAwZTRiMGVlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-276530281\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-131634968 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1XDrxTWmYv3gorVrXvmSodhuVHNiE4Wue14QMI4v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-131634968\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1934332945 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 06:33:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlJmajJKNlA3Ryt1d2FNSmI4eGF3NWc9PSIsInZhbHVlIjoiWmVUbU9LcWVJMjE4TDFCUHJ5bXpEbFdkbHcrQk0xTlF2OVdhRHdiU0pTc3NHQUtLVmRrRFJ4RG1acnFNZDVrd3hXeU5WL1Z4TkRBazRxc3NLUklEek1JSGxlWVJOUXJjd29YeGpJYWtscW9sUHhwdWV2R2FxUi9ZWEhoT2ErNHdGd2plU0RJdzdrRGpmWTlSVjhacHczVFZNNlF2b2dzWUMvMVJsR3V2Zmt0cmVycVpLdnVPWW1KenN5QmVKbVJHd29oOEdFRjRRbkFYdi92YlF3OTNDNm9nWU5PeUFYR0NVa1FtYlJDakNuM0N5cyt3T2tQUy9aT2J1TlJ1Mm52Ykp5RlR1czhaUnkrMWZyZkplOVVCb05IMm5QWFhXZnBib1Y2U20yTXQ2ZzJQZ29MbFltR3pkTWNvYWpYU1orRk12SVRmRmdzNjk4M1pSamZkNE01aUtMcFlJUlYrTkJBdUFzQ2FkUXE2cEt2enAxcVZQUXJuOUY0WWJZS1hWWXVMRkZ2ZVZiTU5iaHc1bi9KUVVFOUdRUFRBT0tmZ3F0VzI1blJvWjBOSmwrWitSTUYvWXdPd1NUMENxOHhOaWlMVFpHTnU1eHJ1WWhvbHIzL1Rja1lKWkI3K2pqT3N3TVg3QjBSc2UyNGtwcXZsTG5aUGkveHZHMlo4SnBGQ1dmWGMiLCJtYWMiOiIyODdiNzYxODFjMmRmYjBlYzA4ZjJjNTlhNmNhM2YyYTY2ZWQxMDdlMDk0OTM0NGQ0NWEzNGM5Nzg1YjhlMjBmIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:33:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlIvY2ZLMGxxOEtlUFBOVnRCd0VmdlE9PSIsInZhbHVlIjoiczBqR0FsckJFZitDeUJNelhBdXZjdFBmcG5hMkhOWTgvelF0UG1Db0t6dXhUbytVQkJOTG9VMWl6NVQ2RE13VDJwWUpVMWNGQitoN0tLQkloSTZJbmdRbzR3MDdwTUZNV1ZLZFo0emZJTzJJcG5kUXY2RUo1Yk1rZlFoZndpVkxzTDk4SWQwNFBEYXdYd3RVM2pKNHMwWjVadjBIV2szUkRpUjZpbERYRU9rTm1pL1pJRDIwZVRxTzBDSWtTWmNubllNKzkxRTRYK3JSSGZ4UEdoWmNKd0VyUFc0cHQzcGZOU3l5RHlUYjRJNVkvVlFvYUIrN3M1U0R1K2RnSzM3TWd4d0phYlFNNkZDcEl3NFRBRXQxWjZTeExBSWJwOE13OXpMWklwVWYzcUhCVU4rakRiUEhwdEg0YjR4NFo0MWlmOGNhckp6L0ljUVlDbHVLOFE1aFA5NThjWUJ1c3hZNU4rSVplU3U3d3BkTDV6bFRkS3FJRzJkK3doQ3pQQ1k3NWFwemFoMUJwUkxXYng3VUZWem1vYU9DenpRZ3Y2UDVKaTV6NHU1MEpYa3JQQnJ2ZGJZYXNPN05lbDlTTWp6b1FmK3dmbnhPWjN2bjRsM1FmRnhPRFV1WkZiaU8wUTA4bE56VDR2SVl0a0Y5VUNHaW1sc0pFQ2JHNEx4TnVWZ04iLCJtYWMiOiI2OTExMzkwMzcwMmM2ZGIxOGY0M2ZlMzYyYmM4OWQ1ODkyN2RiNDg4NjUzODE0NTc3MDRlYWI0MTA1YWQ0NTE2IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:33:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlJmajJKNlA3Ryt1d2FNSmI4eGF3NWc9PSIsInZhbHVlIjoiWmVUbU9LcWVJMjE4TDFCUHJ5bXpEbFdkbHcrQk0xTlF2OVdhRHdiU0pTc3NHQUtLVmRrRFJ4RG1acnFNZDVrd3hXeU5WL1Z4TkRBazRxc3NLUklEek1JSGxlWVJOUXJjd29YeGpJYWtscW9sUHhwdWV2R2FxUi9ZWEhoT2ErNHdGd2plU0RJdzdrRGpmWTlSVjhacHczVFZNNlF2b2dzWUMvMVJsR3V2Zmt0cmVycVpLdnVPWW1KenN5QmVKbVJHd29oOEdFRjRRbkFYdi92YlF3OTNDNm9nWU5PeUFYR0NVa1FtYlJDakNuM0N5cyt3T2tQUy9aT2J1TlJ1Mm52Ykp5RlR1czhaUnkrMWZyZkplOVVCb05IMm5QWFhXZnBib1Y2U20yTXQ2ZzJQZ29MbFltR3pkTWNvYWpYU1orRk12SVRmRmdzNjk4M1pSamZkNE01aUtMcFlJUlYrTkJBdUFzQ2FkUXE2cEt2enAxcVZQUXJuOUY0WWJZS1hWWXVMRkZ2ZVZiTU5iaHc1bi9KUVVFOUdRUFRBT0tmZ3F0VzI1blJvWjBOSmwrWitSTUYvWXdPd1NUMENxOHhOaWlMVFpHTnU1eHJ1WWhvbHIzL1Rja1lKWkI3K2pqT3N3TVg3QjBSc2UyNGtwcXZsTG5aUGkveHZHMlo4SnBGQ1dmWGMiLCJtYWMiOiIyODdiNzYxODFjMmRmYjBlYzA4ZjJjNTlhNmNhM2YyYTY2ZWQxMDdlMDk0OTM0NGQ0NWEzNGM5Nzg1YjhlMjBmIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:33:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlIvY2ZLMGxxOEtlUFBOVnRCd0VmdlE9PSIsInZhbHVlIjoiczBqR0FsckJFZitDeUJNelhBdXZjdFBmcG5hMkhOWTgvelF0UG1Db0t6dXhUbytVQkJOTG9VMWl6NVQ2RE13VDJwWUpVMWNGQitoN0tLQkloSTZJbmdRbzR3MDdwTUZNV1ZLZFo0emZJTzJJcG5kUXY2RUo1Yk1rZlFoZndpVkxzTDk4SWQwNFBEYXdYd3RVM2pKNHMwWjVadjBIV2szUkRpUjZpbERYRU9rTm1pL1pJRDIwZVRxTzBDSWtTWmNubllNKzkxRTRYK3JSSGZ4UEdoWmNKd0VyUFc0cHQzcGZOU3l5RHlUYjRJNVkvVlFvYUIrN3M1U0R1K2RnSzM3TWd4d0phYlFNNkZDcEl3NFRBRXQxWjZTeExBSWJwOE13OXpMWklwVWYzcUhCVU4rakRiUEhwdEg0YjR4NFo0MWlmOGNhckp6L0ljUVlDbHVLOFE1aFA5NThjWUJ1c3hZNU4rSVplU3U3d3BkTDV6bFRkS3FJRzJkK3doQ3pQQ1k3NWFwemFoMUJwUkxXYng3VUZWem1vYU9DenpRZ3Y2UDVKaTV6NHU1MEpYa3JQQnJ2ZGJZYXNPN05lbDlTTWp6b1FmK3dmbnhPWjN2bjRsM1FmRnhPRFV1WkZiaU8wUTA4bE56VDR2SVl0a0Y5VUNHaW1sc0pFQ2JHNEx4TnVWZ04iLCJtYWMiOiI2OTExMzkwMzcwMmM2ZGIxOGY0M2ZlMzYyYmM4OWQ1ODkyN2RiNDg4NjUzODE0NTc3MDRlYWI0MTA1YWQ0NTE2IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:33:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1934332945\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1464073087 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/contact-groups</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1464073087\", {\"maxDepth\":0})</script>\n"}}