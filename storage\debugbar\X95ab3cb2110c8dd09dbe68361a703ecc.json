{"__meta": {"id": "X95ab3cb2110c8dd09dbe68361a703ecc", "datetime": "2025-07-29 06:43:14", "utime": **********.472883, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753771393.587081, "end": **********.472935, "duration": 0.8858540058135986, "duration_str": "886ms", "measures": [{"label": "Booting", "start": 1753771393.587081, "relative_start": 0, "end": **********.395646, "relative_end": **********.395646, "duration": 0.8085651397705078, "duration_str": "809ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.39566, "relative_start": 0.****************, "end": **********.472946, "relative_end": 1.0967254638671875e-05, "duration": 0.****************, "duration_str": "77.29ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2999\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1805 to 1811\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1805\" onclick=\"\">routes/web.php:1805-1811</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "5FqprTJJhidpGbYbZ6m9onDTumcpkjilpP3lhIYm", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-1787760751 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1787760751\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2124520634 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2124520634\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-159773035 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-159773035\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1780810119 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1780810119\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-206401369 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-206401369\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 06:43:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im9oc09jWXU5aE5EeHVjWXYyQ0RKOFE9PSIsInZhbHVlIjoic1BDN1RoZFYrbk5SYzIxRVBncHRnQzkwdXBuZFAxcndtWVZDZlQvMDNPSE5KcXBoRzYzMHgrMDZoMUlRa1BlTVVmOTZSeFZCZzY0ZWJVV1ppYVNBVVM0TFNhWXhqY3A5S2N6L1BISmZzR3dxU3k1eUlPbkI4bmpRbkNsN1R1a1I2UmplcGlNQXp0ai83OFducTNNUHM0a3pKYkd5MWc2ay9RREpEcUFmNTJhajg3RnFydTEybjY0R2tjZW1Cd0dBU2pFR3l1Q3NQYlB4TVZPK1dGWjU0UFR3WDQwS0FzeGhreWV6YStCMmtpY0xiaVRkV05ZUHZ6ZG40RlJCSnNrWkNzbWIzUzJMb0drN1AwVTcweW1YOVVabGxUejR2b3JSOWEwc1J4N1N6NzlIOVZHak9DWm9NOTdkSmJReHFHOFBoZWE4SnFCTWVrK29SaVNXVUl3VGFCYTFvaHBBVld1d2MyN2FyYkZNL2xyRUdZUXZ0RzMvYmQ1S1hQeDlGMlJZZlh3QXRtcFdKek8xTmViUGRRdE9kWWVVc1h2OTJxSnhlOEdyM3JCNjJHREdXWllES1lFNHdxcWxtUnlzeVpFTHRwb0J4OU5WWnhSQStEK1dzZHA4L2xKNzMwR3hxWXdhQjhEVE0xQnFGaUJOREpCMVBMU3h4Qzgwa0tTTExjYmgiLCJtYWMiOiIyOTNlZmU5OGIyMGE5NTllMDJmZTQ0MTU5OWQxNmI1ZmZmMTNhYjkxODZiYTJjMWM2YjkyMmI4MGZlZTFmODVkIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:43:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlJFbURPRHN3TGRmUnhoeFc1dnRyVFE9PSIsInZhbHVlIjoibUJFNkJDdlBtdDJycHRGMnhKWHBFSnZlbllTTDRIY2tyeTRpZ05TN2tVNlhiSitwckhIc3krY05SdTlES2ZZREZDQ3NQZm93YUZBSHJjR09FN2M2VWpWRjAvdWFHQkI5SnhsQ2pmb1BLVWk0OWNIYzFmem11cTRDN25CTXdmQkhXaHUwWmpvMEl5NXpDK3FuWFQ0ZzhOQVZFSnhyMEg4bWhMaGFrVVVrblI5S0tGNlRieG9JZktZbVpabDZnVWx6OTFzMnA2eWtzWkl0RkYycUpyUTJpaU5ZUCtwb25RZUVvUnphNXdWVDFPN2k5c1dPQURCY2xHS2puaENkK1lMRDEwMGRLTEJodHllY0UvRWVlRFh1R2NSVldta2VSbDErdkVmSk5RbTZDanN5OTYzb3I5SC82aEpmaE5VMTZNV1VmNC9vRmxFcnJCSy9VUzZvOS8yNlBSZGFSTDFNcHNaYnpPajMyYmFCWmNWNU9pNzE0WHZDWTVUYTVYd0ZJa0RseU1WNjJHYjZpRjcvcTBaTXh2Qmx2Mk40OXo5SkpYVG03SEcySW5GL1MrczlOSXBtMkU2MXU4ZlZjc3pRb2RKSDVobzZwTlNFcU95cDNyTFZadFc5bVhaemtrRkx5SWRSQzhiQ1JOUlorR0ttTEFaTjV1RFNuVUNkZVRMa2RyRHgiLCJtYWMiOiIzOTdiNTYwZGFjZmFmMjMwYjMyM2M3ZTk4NTNlZWUyYmJhYTlmYzU0MTNiNzJiNjdiYzMwYmM5MWFkNGNiMTY0IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:43:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im9oc09jWXU5aE5EeHVjWXYyQ0RKOFE9PSIsInZhbHVlIjoic1BDN1RoZFYrbk5SYzIxRVBncHRnQzkwdXBuZFAxcndtWVZDZlQvMDNPSE5KcXBoRzYzMHgrMDZoMUlRa1BlTVVmOTZSeFZCZzY0ZWJVV1ppYVNBVVM0TFNhWXhqY3A5S2N6L1BISmZzR3dxU3k1eUlPbkI4bmpRbkNsN1R1a1I2UmplcGlNQXp0ai83OFducTNNUHM0a3pKYkd5MWc2ay9RREpEcUFmNTJhajg3RnFydTEybjY0R2tjZW1Cd0dBU2pFR3l1Q3NQYlB4TVZPK1dGWjU0UFR3WDQwS0FzeGhreWV6YStCMmtpY0xiaVRkV05ZUHZ6ZG40RlJCSnNrWkNzbWIzUzJMb0drN1AwVTcweW1YOVVabGxUejR2b3JSOWEwc1J4N1N6NzlIOVZHak9DWm9NOTdkSmJReHFHOFBoZWE4SnFCTWVrK29SaVNXVUl3VGFCYTFvaHBBVld1d2MyN2FyYkZNL2xyRUdZUXZ0RzMvYmQ1S1hQeDlGMlJZZlh3QXRtcFdKek8xTmViUGRRdE9kWWVVc1h2OTJxSnhlOEdyM3JCNjJHREdXWllES1lFNHdxcWxtUnlzeVpFTHRwb0J4OU5WWnhSQStEK1dzZHA4L2xKNzMwR3hxWXdhQjhEVE0xQnFGaUJOREpCMVBMU3h4Qzgwa0tTTExjYmgiLCJtYWMiOiIyOTNlZmU5OGIyMGE5NTllMDJmZTQ0MTU5OWQxNmI1ZmZmMTNhYjkxODZiYTJjMWM2YjkyMmI4MGZlZTFmODVkIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:43:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlJFbURPRHN3TGRmUnhoeFc1dnRyVFE9PSIsInZhbHVlIjoibUJFNkJDdlBtdDJycHRGMnhKWHBFSnZlbllTTDRIY2tyeTRpZ05TN2tVNlhiSitwckhIc3krY05SdTlES2ZZREZDQ3NQZm93YUZBSHJjR09FN2M2VWpWRjAvdWFHQkI5SnhsQ2pmb1BLVWk0OWNIYzFmem11cTRDN25CTXdmQkhXaHUwWmpvMEl5NXpDK3FuWFQ0ZzhOQVZFSnhyMEg4bWhMaGFrVVVrblI5S0tGNlRieG9JZktZbVpabDZnVWx6OTFzMnA2eWtzWkl0RkYycUpyUTJpaU5ZUCtwb25RZUVvUnphNXdWVDFPN2k5c1dPQURCY2xHS2puaENkK1lMRDEwMGRLTEJodHllY0UvRWVlRFh1R2NSVldta2VSbDErdkVmSk5RbTZDanN5OTYzb3I5SC82aEpmaE5VMTZNV1VmNC9vRmxFcnJCSy9VUzZvOS8yNlBSZGFSTDFNcHNaYnpPajMyYmFCWmNWNU9pNzE0WHZDWTVUYTVYd0ZJa0RseU1WNjJHYjZpRjcvcTBaTXh2Qmx2Mk40OXo5SkpYVG03SEcySW5GL1MrczlOSXBtMkU2MXU4ZlZjc3pRb2RKSDVobzZwTlNFcU95cDNyTFZadFc5bVhaemtrRkx5SWRSQzhiQ1JOUlorR0ttTEFaTjV1RFNuVUNkZVRMa2RyRHgiLCJtYWMiOiIzOTdiNTYwZGFjZmFmMjMwYjMyM2M3ZTk4NTNlZWUyYmJhYTlmYzU0MTNiNzJiNjdiYzMwYmM5MWFkNGNiMTY0IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:43:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5FqprTJJhidpGbYbZ6m9onDTumcpkjilpP3lhIYm</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}