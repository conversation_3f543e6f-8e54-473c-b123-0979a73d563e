{"__meta": {"id": "Xd31cc46d515a0216583447532eb9fceb", "datetime": "2025-07-29 06:32:50", "utime": **********.397365, "method": "PUT", "uri": "/contact-groups/8", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753770769.611528, "end": **********.397386, "duration": 0.785858154296875, "duration_str": "786ms", "measures": [{"label": "Booting", "start": 1753770769.611528, "relative_start": 0, "end": **********.285472, "relative_end": **********.285472, "duration": 0.6739439964294434, "duration_str": "674ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.285487, "relative_start": 0.6739590167999268, "end": **********.397389, "relative_end": 2.86102294921875e-06, "duration": 0.11190199851989746, "duration_str": "112ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50826432, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PUT contact-groups/{contact_group}", "middleware": "web, verified, auth, XSS, revalidate", "as": "contact-groups.update", "controller": "App\\Http\\Controllers\\ContactGroupController@update", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactGroupController.php&line=149\" onclick=\"\">app/Http/Controllers/ContactGroupController.php:149-216</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00847, "accumulated_duration_str": "8.47ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.336418, "duration": 0.0033900000000000002, "duration_str": "3.39ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 0, "width_percent": 40.024}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.35284, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 40.024, "width_percent": 9.917}, {"sql": "select * from `contact_groups` where `id` = '8' and `created_by` = 84 limit 1", "type": "query", "params": [], "bindings": ["8", "84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ContactGroupController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactGroupController.php", "line": 170}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.374646, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "ContactGroupController.php:170", "source": "app/Http/Controllers/ContactGroupController.php:170", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactGroupController.php&line=170", "ajax": false, "filename": "ContactGroupController.php", "line": "170"}, "connection": "omx_sass_systam_db", "start_percent": 49.941, "width_percent": 7.084}, {"sql": "update `contact_groups` set `name` = 'School Management System ok', `contact_groups`.`updated_at` = '2025-07-29 06:32:50' where `id` = 8", "type": "query", "params": [], "bindings": ["School Management System ok", "2025-07-29 06:32:50", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ContactGroupController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactGroupController.php", "line": 182}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.378086, "duration": 0.00364, "duration_str": "3.64ms", "memory": 0, "memory_str": null, "filename": "ContactGroupController.php:182", "source": "app/Http/Controllers/ContactGroupController.php:182", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactGroupController.php&line=182", "ajax": false, "filename": "ContactGroupController.php", "line": "182"}, "connection": "omx_sass_systam_db", "start_percent": 57.025, "width_percent": 42.975}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ContactGroup": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FContactGroup.php&line=1", "ajax": false, "filename": "ContactGroup.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/contact-groups\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/contact-groups/8", "status_code": "<pre class=sf-dump id=sf-dump-1582548848 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1582548848\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1703736971 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1703736971\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1739023214 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">PUT</span>\"\n  \"<span class=sf-dump-key>group_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"27 characters\">School Management System ok</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1739023214\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-432862518 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">488</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundarynhwCHMm3GRdQ8z2v</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/contact-groups</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6Ikd0QzlwWE5oZDBjTjM0Y3MwUUZBZWc9PSIsInZhbHVlIjoiWStjcTdsQkpzOHhGZWJjQWRiVDBBdXN1REVDTWlaYmRqcEU0N3FsUENKMUF5UFA1dENUaEwybk5xL2RMU08vVStVNU0rV29mdWlRT0hrcEtCblY4Vks2ekJ6OXR4bXgya2tNSUU5aVc0YzNQMmxWQlJRN0NLTG4wNUdVSzNXTE4wMWtLWklUVWRqcHZQUXpCU2wxSXdEampLVmcyTUttamJPSkdYMjdDQS9RdUhoYlNkd2h5bFlROUt4SWZ3ZGVRSWRyNFBaQm5MVGdudE9GaFdvYWpkSHVjY0k4dkxlbkMxZkdXQUYwWmpNWTZCSnBJUDZ4dzlNTVRQbHdRWEV5SEkzZm5WY1N3N2R2ZTcwSUtQL0V3cU5HcVd4OVNBaXFObUJLQ2tydWk3QjFxNGgxZ2lPSm81VmdrVEwwS2dlZHJ0Nlg3UFBkWjlOUWJjSFRLajNoOTNCOHVIY2o4dzRqb2FoMVJtSkhIQjRZelF0MnY0UGNObHdDTEpRYVo4YWx0ZlE1UE1YU0gxd2VoQWpKRE9sUmE3a21nUTJqdloydURtSFprOS9icDNpODZVMnNzcWhGUjNUajEvZWx1akM4R0V5eXhoZlVWaWhyY0dUU082NDZFWkFZQnFtY1F6NUUvWm51cGdzd1RZclhlMkZDVVdHK01FRFROK05SeHIvdGwiLCJtYWMiOiIxYTg5YmYyNTE2ZmVmZjcwYzdkOGI4MGM3NGVlZDA3ZWI5Zjk3ZDJlNGJlODAxOTY1MzA2YTBkYzBlY2Q4MGI4IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Ii9vZ0ZJR3prcVdCUDZ1UlYxTTdzMHc9PSIsInZhbHVlIjoiRU5vUmhaNnJsSTN1RDdzT2ZjeWdqQkxVYXduenRrTitJVCs4allJcmFRTGpMQU5LRi91eFRVRWI5V1dUU2hSRmRiT1pXWnpTTlBoMG9GSHlnZjVmTkhYZ2h1U3pmMFBINjllTFJlWmlZaUYvNGNXMVROWEFxVSt0aU00UUZEUW9EZ0Z5M2JrcWVjdjN5VHZSZ1pxZkg5dUxEWEdkVVJuY1JnY2Vjb1kwNjZnRkpQRjhNdEJWeE4xK0lHaWxNclpwWG45TjZiazA4RHJKL0U5SjNpbW1LNW4rSU93T2VNMTNiZVRSVU9LTE04VVZFTzFnN1UwWmUxRHRTTWFhbmxHancvVWg4Q2x3L3RqZUt3aW9HY0lMVjlXeVpUclBacXFkbFp6RGtMN3dFVkhHTlg2a1NlMzdEV2tlaHlKNzRJa2dxY01EQUdwRHdURWVvZWwwR25XZ3ZQWGo2aHVjT1I3SWZxUE9VSjRtK2Q3YUNUdHdvRUdaMEFzUWVURlcvZXpyU215VStaWkZUTlp4VXJ2UGQzVmxJME9kNmY1TGFreGMxOXJQL2c5aFJEWUtrK0tkNytZcDBsMUFDRHhRNk96ZTJpTlVkbzFRdzU1OFU0SjgxL2EySStYWUNzUExtc09IZTdwbVJYTUJiZERQV2hzZmk2UWlnMTZpVkZwbjVuTjMiLCJtYWMiOiI1MDk0NDM4OTRjYzQzY2QxNGE0NmNiZjVjZWJkMzNlOTBjM2ExMDQwMzY1Yjc0ZWJmMDc0ODlhZjkzNDBlNjA4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-432862518\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-401700852 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1XDrxTWmYv3gorVrXvmSodhuVHNiE4Wue14QMI4v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-401700852\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1126786141 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 06:32:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InAycHNnakVxL2VKNTg4US9PYVFOelE9PSIsInZhbHVlIjoiUVhacE5tL3pRVEtZZ1hnRFF1TXNMK1pPTXM0a3ByZkRPSzA4bXVGdXdlUTNxdlZqR0loS3VOZlBIUjdPR28yN0U2dGdYREd0bmsrSFV1N1pvSjAvbFhmMlVHcjlVT05NMkMyTllJZ3FGQUs5OXlTVFB5cndUd3FQMHpzeUUvcW5qMFlQVUFSVVFXMmJVWXdpMC9ldjlFc09NUFc1cEZQK3NlOWMxUVN2YzYvOEsxZU10YUpqZ005c21qdWx4UlNWRGZCaUl0OWJVN0NBaXIrT0RHa3FWZWVRUFNtekJrVjhTenVLV2dGVXV2S3hSM0lFZmJtcUZYZkpPVmdOUUp0K05DbmUzSEJHUHRQaEZyK0RxTXVDblJqU1JCM3ZuTVJjSXg1a0Z5ZkROMUhhY0lhM2NrWjE1LzJuWjFmQ29jZHZnamwyQU1ESUVHSGJwcnA3M0NxSVFNckpwS0tsTHhra0M5YmJRbmRBa2l2dUtOUVpkSndUaVMzWUdKT0o2WXN4T0NCZ1ZNVHJCNlgzSjJ3ZTEvSlZwaUxPd1BBZGpMQ1huelIyY3FyNkZTMkxraEFlUVRFK0xoNE1tREJaMkF0S0hydVEraitrUThWNHRwc2VoSDlyTXp0RmlsMkw1QmNBaGk5OWp2Q1VWUjB0cVZiQWp2MEViaVdKcmYraVdSUU8iLCJtYWMiOiJhYjY5MDgyOWU0MDlmZTQxOTcyMTMzMTQwZWJkOTU4MjYxZDFmOGQ0NDBmYmEyY2I2M2VkYzBhZDdkZGNkNjdiIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:32:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkZSV3E5UldBaUVNMEM2WkcvalFLUnc9PSIsInZhbHVlIjoiSGlmWUhybDlUcUVieEtMNjhkN294bU5pMlc2WnJrVnlDYUNRcU9wNEpROWR1MEFXWmJRa2xvRXNxMjBLNmpiSEtzNVd5M0IwWWIvZTdyTzA5c29xU2NQZityTUVIa3JrWjNWYVd4bm56azdFVnFUb0dwRWhheGNPK2RvN2cvMm85NHEyMXR4WDNnM0VaTllCRGxXYmNha3hXNi9ZZlU3c2JRc1hBd0xvM0pnUldUaWZtTkp0aVBWVnBaY1NlSlgyeE9kamJMc2JyZHhBUnlheTBjaW96VE1FdEkyU3dOUll0Zm5sRnptUmwxVWdCcnRuT0RCVnI0QTl4RC9WZStmM1NBaTRUV0JIMlNhUjJJdkk5L1lBd2hRUTV6dWdpRHRIVWtwb0pvVUZBZk84M2w3WU1HWlBWa2JYOU9QYUVkbzZhb1d2V3RqaE9qcFZDK0swZFU1V3hiSVJTYWM5eWE1dzN5di84WGVxTUg4WVdERzZiL0M3OWJEa2pIZTE4Y0tLd3d1MWcvY0dvcUJlakNKSjc5Skx0Y29veFdDY2ZvejAwVkxRSnVNcnhFeVB2OVJHaFBPWENnR1EyVDFTQ2hyZ1UwbnFGTXZ4a0pCY1NyQlk4KzRzdHR5U25idDBUREtUR1U5N2tZY1RzN21KRnpZcTkwbG45dFJpMGs5UjV6d2siLCJtYWMiOiI0ZjFlMmM2ZTMxNGZhYTBjNzQxYWM2ZmMxYTc0NmJmYmNlNThiM2RlMTE4ZDgyZWJhZWExNDI3Njg0Yzg3ZDkyIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:32:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InAycHNnakVxL2VKNTg4US9PYVFOelE9PSIsInZhbHVlIjoiUVhacE5tL3pRVEtZZ1hnRFF1TXNMK1pPTXM0a3ByZkRPSzA4bXVGdXdlUTNxdlZqR0loS3VOZlBIUjdPR28yN0U2dGdYREd0bmsrSFV1N1pvSjAvbFhmMlVHcjlVT05NMkMyTllJZ3FGQUs5OXlTVFB5cndUd3FQMHpzeUUvcW5qMFlQVUFSVVFXMmJVWXdpMC9ldjlFc09NUFc1cEZQK3NlOWMxUVN2YzYvOEsxZU10YUpqZ005c21qdWx4UlNWRGZCaUl0OWJVN0NBaXIrT0RHa3FWZWVRUFNtekJrVjhTenVLV2dGVXV2S3hSM0lFZmJtcUZYZkpPVmdOUUp0K05DbmUzSEJHUHRQaEZyK0RxTXVDblJqU1JCM3ZuTVJjSXg1a0Z5ZkROMUhhY0lhM2NrWjE1LzJuWjFmQ29jZHZnamwyQU1ESUVHSGJwcnA3M0NxSVFNckpwS0tsTHhra0M5YmJRbmRBa2l2dUtOUVpkSndUaVMzWUdKT0o2WXN4T0NCZ1ZNVHJCNlgzSjJ3ZTEvSlZwaUxPd1BBZGpMQ1huelIyY3FyNkZTMkxraEFlUVRFK0xoNE1tREJaMkF0S0hydVEraitrUThWNHRwc2VoSDlyTXp0RmlsMkw1QmNBaGk5OWp2Q1VWUjB0cVZiQWp2MEViaVdKcmYraVdSUU8iLCJtYWMiOiJhYjY5MDgyOWU0MDlmZTQxOTcyMTMzMTQwZWJkOTU4MjYxZDFmOGQ0NDBmYmEyY2I2M2VkYzBhZDdkZGNkNjdiIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:32:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkZSV3E5UldBaUVNMEM2WkcvalFLUnc9PSIsInZhbHVlIjoiSGlmWUhybDlUcUVieEtMNjhkN294bU5pMlc2WnJrVnlDYUNRcU9wNEpROWR1MEFXWmJRa2xvRXNxMjBLNmpiSEtzNVd5M0IwWWIvZTdyTzA5c29xU2NQZityTUVIa3JrWjNWYVd4bm56azdFVnFUb0dwRWhheGNPK2RvN2cvMm85NHEyMXR4WDNnM0VaTllCRGxXYmNha3hXNi9ZZlU3c2JRc1hBd0xvM0pnUldUaWZtTkp0aVBWVnBaY1NlSlgyeE9kamJMc2JyZHhBUnlheTBjaW96VE1FdEkyU3dOUll0Zm5sRnptUmwxVWdCcnRuT0RCVnI0QTl4RC9WZStmM1NBaTRUV0JIMlNhUjJJdkk5L1lBd2hRUTV6dWdpRHRIVWtwb0pvVUZBZk84M2w3WU1HWlBWa2JYOU9QYUVkbzZhb1d2V3RqaE9qcFZDK0swZFU1V3hiSVJTYWM5eWE1dzN5di84WGVxTUg4WVdERzZiL0M3OWJEa2pIZTE4Y0tLd3d1MWcvY0dvcUJlakNKSjc5Skx0Y29veFdDY2ZvejAwVkxRSnVNcnhFeVB2OVJHaFBPWENnR1EyVDFTQ2hyZ1UwbnFGTXZ4a0pCY1NyQlk4KzRzdHR5U25idDBUREtUR1U5N2tZY1RzN21KRnpZcTkwbG45dFJpMGs5UjV6d2siLCJtYWMiOiI0ZjFlMmM2ZTMxNGZhYTBjNzQxYWM2ZmMxYTc0NmJmYmNlNThiM2RlMTE4ZDgyZWJhZWExNDI3Njg0Yzg3ZDkyIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:32:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1126786141\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1480725597 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/contact-groups</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1480725597\", {\"maxDepth\":0})</script>\n"}}