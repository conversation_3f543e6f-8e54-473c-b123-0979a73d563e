{"__meta": {"id": "Xa31de31f8788ece800f033f664fe481f", "datetime": "2025-07-29 07:09:03", "utime": **********.568043, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753772942.695702, "end": **********.568076, "duration": 0.8723738193511963, "duration_str": "872ms", "measures": [{"label": "Booting", "start": 1753772942.695702, "relative_start": 0, "end": **********.481079, "relative_end": **********.481079, "duration": 0.785377025604248, "duration_str": "785ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.481093, "relative_start": 0.****************, "end": **********.568079, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "86.99ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2999\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1805 to 1811\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1805\" onclick=\"\">routes/web.php:1805-1811</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1Lf9o9bynMJazeU3PACo36ba9jqee686v3j8FMri", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-1957098867 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1957098867\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-625227331 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-625227331\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-406027178 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-406027178\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-836606812 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-836606812\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1540913757 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1540913757\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 07:09:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBhVk5sZy9NNWhENmJ2R2NxTkkzRVE9PSIsInZhbHVlIjoiVWk2bHFQQjFtdGtsNUppMHYrbXlpaFpHN0lEN1NTWkc3MnBiZHQydnBGbWVXa2h4K3hsZ1k3dE9jWnpkL1lnZnVsOEVKRlNINHBTZFB2Rm1pUyszS1lBWVFGYU9vMEpwcEFjamUwczlqeWF0YVd3WWp2MU1xNDd2MFMrS1FBVTNuS0FFWXRoZ0ppSzloT0Q4UVdZbEVITENFcStOdWxpSHVYNlFsdGp5RjBpOWtZY1UwaDNhWWhoZHU2Q0JoQlVlZjhCS2IwT2Vvd2ltMlI4MWRlOVRpRFhRSlplb1RRbFJrVUNtRjREdnlNMFF1MWExLy8ydDBmdjR6dW1ZaEtEUG1PTlJEM2hNZ2xBdVhEcTNpd2ZZSkhoMDBzUzNvTWdISnFtY3VYWnJWNURDVVA1eWR1aUhxV0RGeWlqR3Ayak1VNWtKdFJmUUgySW9PNGlhNnJhcEk3TEx4Tlp1L2NKTXREYmZYaU9WVXo1VkQrazZIRkZhcEY0NU9iYWliKzB3dmVZdWVwRUJJMUNEOGNWTmxqL1dPcXV4alUraWJZbUlVNnNLQU9OSmdlOEZFakpoRXd2WWpWNWJtNUh1eDBSRWR2Z0s1SmpiNXVPaEJuVUpUKzZOcHpoSlhDVHhQN2c2MGZaVUIrUlF5L0w5RHRCNU42UmlUd3RqemtoQmFxam8iLCJtYWMiOiIxM2FjY2ZjYTc4MzFjMDBmNDdmOTJjYTQwMjcwZDYyNjVjMjYwY2ZkYTM3MzY1MzRlNjFkNWZhMDcwZDBmNjRkIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 09:09:03 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Iks3OFdpSXNuV296MTFTa3hCaThhM1E9PSIsInZhbHVlIjoiT25ORUh0WVV4QzJaekFQVUVjU21RSUR4Vm9jdTBQOVhudUJHeE15OFNjdW1KdFZRMG4zMjN3VGlhUHVEL05RTEZYOVBEL2xtdkJYaG4yMkdEdCtFZzBaakRwSy9JendYalFwZ1FIZG92TVNkVk5qM1kvaUdrSS8xbGtocGlseU44aUZiK3VGK0RKeEI3L0dNN3hIL3h5c0E1cVoyeVRHM1lmNFVRQVFBSDgvR1ExOVN2UEpCQnpERGYxam1xalVmaFlVSHozTTV6Q2dob3N0ZkU3aHJFMDBqclhwRWVPR3plRVA2TU1SVVRsc1c2Y2hmczQzY2hiQW5aeGd3OFFuaTBzYnAwWnlvTXN3Q0YwVEkzcS8wZ0xqdWZJVnFmMXE0SnZrd091cTQyNFFmbHZsMDVYcXY0aXJ6NUVPbEs1b0VuNHB4NG9FdVRramY3RmM0Q1hpdjNkYmZqNXk3cnJpQzBvTFJONUtDSTJwTUo5TGRiRXUwK3JEL2lkK2k1NWIrSjV0MGJURCtLUVl2cWNrTi9jMG9zcGg1azh3QUhacU96aWw3RW9LdzgrSXBHaCticjhRcEdFNGlVTGF4eFNwZmNXZ0szRXNJREdLQU1BZkpiWWZJbWF2d3FLalJiOWJyTThoY2RNVjc2NjNUdm9PSThDWkp2ZStJOWhmdkVzcm0iLCJtYWMiOiJkN2JhZmM3Y2IxMDhhMGNjZDQ4MWFhZmYyNzg4NmY2YWMzMDIyNTUwNTFiMWIwODhjNTAxMGE2NTMwZjI0YzE3IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 09:09:03 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBhVk5sZy9NNWhENmJ2R2NxTkkzRVE9PSIsInZhbHVlIjoiVWk2bHFQQjFtdGtsNUppMHYrbXlpaFpHN0lEN1NTWkc3MnBiZHQydnBGbWVXa2h4K3hsZ1k3dE9jWnpkL1lnZnVsOEVKRlNINHBTZFB2Rm1pUyszS1lBWVFGYU9vMEpwcEFjamUwczlqeWF0YVd3WWp2MU1xNDd2MFMrS1FBVTNuS0FFWXRoZ0ppSzloT0Q4UVdZbEVITENFcStOdWxpSHVYNlFsdGp5RjBpOWtZY1UwaDNhWWhoZHU2Q0JoQlVlZjhCS2IwT2Vvd2ltMlI4MWRlOVRpRFhRSlplb1RRbFJrVUNtRjREdnlNMFF1MWExLy8ydDBmdjR6dW1ZaEtEUG1PTlJEM2hNZ2xBdVhEcTNpd2ZZSkhoMDBzUzNvTWdISnFtY3VYWnJWNURDVVA1eWR1aUhxV0RGeWlqR3Ayak1VNWtKdFJmUUgySW9PNGlhNnJhcEk3TEx4Tlp1L2NKTXREYmZYaU9WVXo1VkQrazZIRkZhcEY0NU9iYWliKzB3dmVZdWVwRUJJMUNEOGNWTmxqL1dPcXV4alUraWJZbUlVNnNLQU9OSmdlOEZFakpoRXd2WWpWNWJtNUh1eDBSRWR2Z0s1SmpiNXVPaEJuVUpUKzZOcHpoSlhDVHhQN2c2MGZaVUIrUlF5L0w5RHRCNU42UmlUd3RqemtoQmFxam8iLCJtYWMiOiIxM2FjY2ZjYTc4MzFjMDBmNDdmOTJjYTQwMjcwZDYyNjVjMjYwY2ZkYTM3MzY1MzRlNjFkNWZhMDcwZDBmNjRkIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 09:09:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Iks3OFdpSXNuV296MTFTa3hCaThhM1E9PSIsInZhbHVlIjoiT25ORUh0WVV4QzJaekFQVUVjU21RSUR4Vm9jdTBQOVhudUJHeE15OFNjdW1KdFZRMG4zMjN3VGlhUHVEL05RTEZYOVBEL2xtdkJYaG4yMkdEdCtFZzBaakRwSy9JendYalFwZ1FIZG92TVNkVk5qM1kvaUdrSS8xbGtocGlseU44aUZiK3VGK0RKeEI3L0dNN3hIL3h5c0E1cVoyeVRHM1lmNFVRQVFBSDgvR1ExOVN2UEpCQnpERGYxam1xalVmaFlVSHozTTV6Q2dob3N0ZkU3aHJFMDBqclhwRWVPR3plRVA2TU1SVVRsc1c2Y2hmczQzY2hiQW5aeGd3OFFuaTBzYnAwWnlvTXN3Q0YwVEkzcS8wZ0xqdWZJVnFmMXE0SnZrd091cTQyNFFmbHZsMDVYcXY0aXJ6NUVPbEs1b0VuNHB4NG9FdVRramY3RmM0Q1hpdjNkYmZqNXk3cnJpQzBvTFJONUtDSTJwTUo5TGRiRXUwK3JEL2lkK2k1NWIrSjV0MGJURCtLUVl2cWNrTi9jMG9zcGg1azh3QUhacU96aWw3RW9LdzgrSXBHaCticjhRcEdFNGlVTGF4eFNwZmNXZ0szRXNJREdLQU1BZkpiWWZJbWF2d3FLalJiOWJyTThoY2RNVjc2NjNUdm9PSThDWkp2ZStJOWhmdkVzcm0iLCJtYWMiOiJkN2JhZmM3Y2IxMDhhMGNjZDQ4MWFhZmYyNzg4NmY2YWMzMDIyNTUwNTFiMWIwODhjNTAxMGE2NTMwZjI0YzE3IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 09:09:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1Lf9o9bynMJazeU3PACo36ba9jqee686v3j8FMri</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}