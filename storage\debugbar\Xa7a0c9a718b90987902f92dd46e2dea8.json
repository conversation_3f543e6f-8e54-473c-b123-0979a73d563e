{"__meta": {"id": "Xa7a0c9a718b90987902f92dd46e2dea8", "datetime": "2025-07-29 06:32:18", "utime": **********.15673, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753770737.002932, "end": **********.156768, "duration": 1.1538360118865967, "duration_str": "1.15s", "measures": [{"label": "Booting", "start": 1753770737.002932, "relative_start": 0, "end": **********.061108, "relative_end": **********.061108, "duration": 1.058176040649414, "duration_str": "1.06s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.061129, "relative_start": 1.***************, "end": **********.156771, "relative_end": 2.86102294921875e-06, "duration": 0.****************, "duration_str": "95.64ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2999\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1805 to 1811\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1805\" onclick=\"\">routes/web.php:1805-1811</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "BVD06einldGfdPB3XYjQaC7M3sXWjSeEObnomHWh", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-781148472 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-781148472\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-834101779 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-834101779\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-408321735 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-408321735\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-159426295 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-159426295\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1519749989 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1519749989\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-420745795 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 06:32:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlhWYmZDVUV3bjVUL1BlUWlYRzBIdXc9PSIsInZhbHVlIjoibmY0Yy8xYWg2VWJwZVFYU0UwWDBmeTMyWko5MUdrRjRQQ0l5TGZFaHBlUkx0dThTQU5pTkxZU1prazIvdTI5anNhajJWS2Y3ZTZtVDNrdzZJOSthNHM3RVd6cHdZQ2R4R3NsUVNHOHJ6SGE3MldoZkg5dDZtaTdjQU1Pa1BSOFBFVVViQ3B1TVJsL3puSXJNWjBWaGd6SytxQkY2TGN5TmdJbXREQXpnL3lEbnpWcURxa1I3WTlBVUpTUDJaaEtTWmdwZDcyOVpvL1FLUzliYzlTbnJJNkRTUVpVbHl6WXdNRlljVUJWdkd1NVNpOUl5eWdsazFyeXdJbUR2d3REZWc3bWRYemE5SHN3SC9CRnI4VHY2WW1Qamt4TkxEaEZSVWFGdHhJMjlSOXU3R01GYlcyUm9JQU5sR3JPSDhiQk9uaFp1Vlc4LytQQWVTRGRLeW5nOW8zeXB0blFrYS81TjlucXNSeWY5L2tQcnpOUEpUU1RRaWFGOHVyME1CNm5DTExMdGxlb3dCK3NPWG5DaXhBSWFGS1dkUSt4WmMwSHdvN2RuWEIxUk1oRno3dy9STXYwNEw4QUVYZGsyUzRFTlNUMThXWTRna0NDQThrMEhnVlNhbFVpRXRBQXdtTDNCd1dSZzdFS1RCVmRaN0hITzhmbjJZT3FnVndleERuMU4iLCJtYWMiOiI2ZDZhNjExMGVlZWIzODg3ZTNiYTEwMmY2ZmNhNGYzODZkN2IxYWM3N2IzNTk4NzEzYjk1MWFlMzkyODk5M2Y4IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:32:18 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlVBdmdmOS9VaTBXdCt0eGJQZEMyUkE9PSIsInZhbHVlIjoiaFJrN0NIdFp4eGhxd2xCK1FzSzcrYWhydjY2OXJDckNkdVUyYXdnQ1M1NWNxVWYrR1BLc2dTWjlRamtpb2dnckkwYWIyWWU5c3JSejlHTitkOHF4SXRBbUs4MXNOeWpDOUFWU21pbk9TbDNkWnVxSjh2TlZ3N0lSa0pKMytlNnVpUFBZMHJiNE5HNkkxd3NmKzl0TTRhdUN3TzB3cnpCUDBpN3plemNpMWxpYjNzMzdQUDhwcmowSjVKSEU3WDlkU25ESXpmSUZwdjNNenRNeVUrelJUWUhSRm9Qd1NsckRFeENQdGtlV0dUcTlJcDltRXl0L1dKdjRJS29za2xrL2Roc2tUVWhIQ0pPYXhXNTZUSkRWcEgyZGQxT2dXazE4NE9pakFtdnNINWZEZldNQXpubmF5TW9yZHlIREl4YkROUE5NWDREVnhzaGRCK0U4RFJHZlJGL0JoWDJONHdJenAvWTA0RjZ5RXJabk9CT2QrZlNPVDNKd2VNQ1RKd1drMTFZVGFsV2w0WStrNDcvNWI1VGRQRkF4U1JUMW4rc093M3dESGw2UWYzNlAxVkhCSlZJSElwZExsblI5eXdjMWNzOWt4ZkFyY2VXanBaazVkWHJsWXE0eitHRWZkQVluTW5UeTZqOGNJVlc1K3VVemJKd2dDemhwc2xUSTJpYWkiLCJtYWMiOiI0OTQ1MTliODdhZjM5YTc2ODJmZGM3NzBiZjFmN2VkZjE1MmJmZjM1MWE4ZTVhMWI2YTQ4NTg0N2JiYTkzOWU3IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:32:18 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlhWYmZDVUV3bjVUL1BlUWlYRzBIdXc9PSIsInZhbHVlIjoibmY0Yy8xYWg2VWJwZVFYU0UwWDBmeTMyWko5MUdrRjRQQ0l5TGZFaHBlUkx0dThTQU5pTkxZU1prazIvdTI5anNhajJWS2Y3ZTZtVDNrdzZJOSthNHM3RVd6cHdZQ2R4R3NsUVNHOHJ6SGE3MldoZkg5dDZtaTdjQU1Pa1BSOFBFVVViQ3B1TVJsL3puSXJNWjBWaGd6SytxQkY2TGN5TmdJbXREQXpnL3lEbnpWcURxa1I3WTlBVUpTUDJaaEtTWmdwZDcyOVpvL1FLUzliYzlTbnJJNkRTUVpVbHl6WXdNRlljVUJWdkd1NVNpOUl5eWdsazFyeXdJbUR2d3REZWc3bWRYemE5SHN3SC9CRnI4VHY2WW1Qamt4TkxEaEZSVWFGdHhJMjlSOXU3R01GYlcyUm9JQU5sR3JPSDhiQk9uaFp1Vlc4LytQQWVTRGRLeW5nOW8zeXB0blFrYS81TjlucXNSeWY5L2tQcnpOUEpUU1RRaWFGOHVyME1CNm5DTExMdGxlb3dCK3NPWG5DaXhBSWFGS1dkUSt4WmMwSHdvN2RuWEIxUk1oRno3dy9STXYwNEw4QUVYZGsyUzRFTlNUMThXWTRna0NDQThrMEhnVlNhbFVpRXRBQXdtTDNCd1dSZzdFS1RCVmRaN0hITzhmbjJZT3FnVndleERuMU4iLCJtYWMiOiI2ZDZhNjExMGVlZWIzODg3ZTNiYTEwMmY2ZmNhNGYzODZkN2IxYWM3N2IzNTk4NzEzYjk1MWFlMzkyODk5M2Y4IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:32:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlVBdmdmOS9VaTBXdCt0eGJQZEMyUkE9PSIsInZhbHVlIjoiaFJrN0NIdFp4eGhxd2xCK1FzSzcrYWhydjY2OXJDckNkdVUyYXdnQ1M1NWNxVWYrR1BLc2dTWjlRamtpb2dnckkwYWIyWWU5c3JSejlHTitkOHF4SXRBbUs4MXNOeWpDOUFWU21pbk9TbDNkWnVxSjh2TlZ3N0lSa0pKMytlNnVpUFBZMHJiNE5HNkkxd3NmKzl0TTRhdUN3TzB3cnpCUDBpN3plemNpMWxpYjNzMzdQUDhwcmowSjVKSEU3WDlkU25ESXpmSUZwdjNNenRNeVUrelJUWUhSRm9Qd1NsckRFeENQdGtlV0dUcTlJcDltRXl0L1dKdjRJS29za2xrL2Roc2tUVWhIQ0pPYXhXNTZUSkRWcEgyZGQxT2dXazE4NE9pakFtdnNINWZEZldNQXpubmF5TW9yZHlIREl4YkROUE5NWDREVnhzaGRCK0U4RFJHZlJGL0JoWDJONHdJenAvWTA0RjZ5RXJabk9CT2QrZlNPVDNKd2VNQ1RKd1drMTFZVGFsV2w0WStrNDcvNWI1VGRQRkF4U1JUMW4rc093M3dESGw2UWYzNlAxVkhCSlZJSElwZExsblI5eXdjMWNzOWt4ZkFyY2VXanBaazVkWHJsWXE0eitHRWZkQVluTW5UeTZqOGNJVlc1K3VVemJKd2dDemhwc2xUSTJpYWkiLCJtYWMiOiI0OTQ1MTliODdhZjM5YTc2ODJmZGM3NzBiZjFmN2VkZjE1MmJmZjM1MWE4ZTVhMWI2YTQ4NTg0N2JiYTkzOWU3IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:32:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-420745795\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1890040640 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BVD06einldGfdPB3XYjQaC7M3sXWjSeEObnomHWh</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1890040640\", {\"maxDepth\":0})</script>\n"}}