{"__meta": {"id": "X184c7224a3fb1a5b62dfee33cc01078c", "datetime": "2025-07-29 06:33:37", "utime": **********.078046, "method": "GET", "uri": "/contact-groups/8/members", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753770816.170904, "end": **********.07807, "duration": 0.9071660041809082, "duration_str": "907ms", "measures": [{"label": "Booting", "start": 1753770816.170904, "relative_start": 0, "end": 1753770816.957134, "relative_end": 1753770816.957134, "duration": 0.7862300872802734, "duration_str": "786ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753770816.957198, "relative_start": 0.7862939834594727, "end": **********.078072, "relative_end": 2.1457672119140625e-06, "duration": 0.12087416648864746, "duration_str": "121ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46652136, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET contact-groups/{id}/members", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\ContactGroupController@getGroupMembers", "namespace": null, "prefix": "", "where": [], "as": "contact-groups.members", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactGroupController.php&line=328\" onclick=\"\">app/Http/Controllers/ContactGroupController.php:328-372</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00981, "accumulated_duration_str": "9.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.028436, "duration": 0.00664, "duration_str": "6.64ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 0, "width_percent": 67.686}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.050241, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 67.686, "width_percent": 9.072}, {"sql": "select * from `contact_groups` where `id` = '8' and `created_by` = 84 limit 1", "type": "query", "params": [], "bindings": ["8", "84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ContactGroupController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactGroupController.php", "line": 333}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0559318, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "ContactGroupController.php:333", "source": "app/Http/Controllers/ContactGroupController.php:333", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactGroupController.php&line=333", "ajax": false, "filename": "ContactGroupController.php", "line": "333"}, "connection": "omx_sass_systam_db", "start_percent": 76.758, "width_percent": 14.883}, {"sql": "select * from `leads` where `contact_group_id` = '8'", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ContactGroupController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactGroupController.php", "line": 342}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.063225, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "ContactGroupController.php:342", "source": "app/Http/Controllers/ContactGroupController.php:342", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactGroupController.php&line=342", "ajax": false, "filename": "ContactGroupController.php", "line": "342"}, "connection": "omx_sass_systam_db", "start_percent": 91.641, "width_percent": 8.359}]}, "models": {"data": {"App\\Models\\Lead": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ContactGroup": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FContactGroup.php&line=1", "ajax": false, "filename": "ContactGroup.php", "line": "?"}}}, "count": 4, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/contact-groups\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/contact-groups/8/members", "status_code": "<pre class=sf-dump id=sf-dump-1587390232 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1587390232\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1107569114 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1107569114\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1821676435 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1821676435\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/contact-groups</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6Ikl2V0R6bVNHYTN4WlBXdXdwZWdmekE9PSIsInZhbHVlIjoiQnJVOFlObmZqVmQ1b1p0eXVsOGhiMWZxWHFseXRmclllaXUyOERmMFFYZWdaWFhFdG96SGVyVkRuSTFobGtEU2pRQTI0bTlaWmlJL1ExTFpwSWttd3FzVkZsMHU3RXliN1JOTmNnZlV2QUpOU3poRFhLdmtFUG0xNGlzdEdlcTdQUXpsL05jZ2ZINDZjdG1uWHhtVGZFTExaNVk2L3RWTG80Vks2eExRejlJYnZtTVRZKzh6bnlwNURjZWNaR0JENXVqR0g4SXppTUFZYWtZVC8wQ2R0dFgyRVFqZTlEQms1SExDTWVrYzZYdDUveFdRYVY5TGpvWjE5TVJyOHA3aGlUZFdrdkQ3RG9lTnFXbGV0cXZObkN5OGZibWtDTWVLSXRCRWJXdFdPTjNlMkNHOHdWRWw1KytOL0k2V3E1eDNGNWMvUDZ5NDE0OTdpalJmek8rRCtTNUtwSWJCSFJTZ3dIbjQrT09EekNVdHhKdzNzVXRZMVVhYWdtRDFDWWtrenNwSG0zVVpVSDkxNzg3M080RVBiNXQ2aW5yem9HbUVrbnBMVmxLYlZ1UCtGempWd3Njd2dZZG1HK0tjK3luMEx4K1cvNmN5WUIvY0dtaC9QYW40UXptZEdycnhmejlaTlM0TGpLSUl4SFFBNUN4MWJ3RXNFNUZQZStQS2ZSWmwiLCJtYWMiOiIwODgxMDI2NWQ1YTQwNTA4MWZhM2JkM2JmMDcyMWFmOTgyNDQwNGRmZDIzMTM1MTMxN2Y1NDk4MTYxMGJiYjI3IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlNyTU41aWJhT0xBUEdmQXlsVjhMTnc9PSIsInZhbHVlIjoiKzBQTytIYnhiTW5Nd1JXZ2Z3WnE0N1gyWU96d1ZzTE9PUmZxdUdDTlBUK0R0cW8xNTlYYzkxa2hDdm9LcjdqY3oxNnpMRVc1UUhDUHNIdjREOFgvVHVVQkwrc2tWQVNiYWttSUxiOTA2NG5HWC8rbkNYZ2VjNlh1Tk5CUk9ZRmFVYzdGZnNkQWJYNCtWOGVHMi9ndjJkQlluSlZ6blZrZXRVRUxZSmhxajFNNXpzSG00T2xaZXFJdTlablgxNDhpSHYwUlUrTUptMDJ5SlNPcEVzNWdUZEVtc05PcnZ4ekV3dGFPS0hLNmtUdk1UYm5jV3ZsOGpYYmpkY2lZYVFGQTZwOElLaTd4QVN3cHd6WCtrN1VsVGhpRkFQRGF5TURhWmk5R2hhVkNjcmR2Z2w1TXFTdDBCZnRpQXNYM05qUlgrWXY1b25UMXQ3bkM2bDlIZXpjSDNDWnJ2cFZWVVVGcVM0dlJ1YUluOVZXdmRCTFN0bnZjZDh6MnFYUVFvRGFoVFNxYy9NL3hGLzJKeUZxdUlWSkhrUkhsMmdiKzB6dWJqT1dyUXJicVd4U1Bsdnlhb3I4RGRla2RBQU9UblhFOVM4a040dCtlek9LaUxGUXN0VGROdVZBNHZYQ3FmZFFONEtlbFcrTGVMV3lKOENqaURldW5qdG9uZE5MbnRmVXUiLCJtYWMiOiI3YjIwNGNmNGYxYjc4OTlhN2QyMzNjYjk3OWFhNjU1NWU3NGY4ZGUzMjY4MDJhYjFmM2EyYTExMDYxYThjMTk3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1XDrxTWmYv3gorVrXvmSodhuVHNiE4Wue14QMI4v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2099447470 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 06:33:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlArUnF5QnhqUTdtYUs2K0FqU1pIQ2c9PSIsInZhbHVlIjoiaG1rbkl3MXJZdUFVakJUWHVWNmFzWVZadGczLy95VU5Db3psalNNMlRORzlVL3kxbE0wN0t4eUQvbkY4ZXR5QmxsalcwcFU4TEpHT0xpWi9hSHF4amVSK3FoemlacVhUOHpHK3JuVkpjYk5vYkxxNEFNZmNIaWUxQndJdEd5ZHY2eDdtZUVYem1RRnVxYzFDbHU3UVNpKzM5VW1udFBnMGY1TFhqcEpKM2oyTlEyZHBYTnptUG9TNitXNTZncGYwRW5qRnFubGh1RkVOMnNlQmdQY1p3dnhLK2s3TmJPV3hFVDFMdzRrcGlSeHY5RmtRai9HSWxtRHdiL3BPN0ExNDhzSkVTa3orWkdRV3dSNFhUUlZlanVnaWVCK0hHcGVnWEZJNjllUnFuckxoK2ZwYmdHR2x2ck5hak41aGpsUndyOXlHZFFnSmFkTGZVOHNQQ3lXNktjWDFUc1l1dHB6SWRjQ2JhYVY0RE9lWlpIcEppaHdZZnBHWTIxTjRWN2RCaWhPNDZkQXd2UW1sYjB3QVhGeHpOTXJaZFN6ZDYrNncvdHJwSWR2T2hoeDNVUU95TXpRZEVDZEkyNGFWdTFLRGhUYW9KYUNLSDNHb0xoMWMwcTF1dVV2VndQY3AvdEhtQkZYcWJnWk1MUzdnWGF1bzR5WTNWNExZeUxBNjhNeDYiLCJtYWMiOiIzZGZmZWZhMjM4ZDcyZGI4NTg5N2ViYjdkMDNhODkwZGFkNzM2ODhkNTlkMDViMDVhMjgxMjI3MjQ2NDdjZTMxIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:33:37 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjNRajV5ODVxREVBMmsySTJ3dWNDbkE9PSIsInZhbHVlIjoiZUE2WFppYnJrRDA0by9yeTRrZVNWU1dOeEJ4NXVNeGlVU2pvcEZaUkFVNFA1aWRlbk0wWXhIc29WVzVLSFprTERFQ1d0bExHckxqbmlJa0FJZ1NzQTdQdEdhUm1WcTJvdDVRcnJ4bmw1YThLSElBSE5xOTUyL3NEb0tnV0hSMHZ3ZksyNUlxS0VNWFh6QjVBOTQvZHlOR29NdUk3T0lCaEVyRmloc2RLTlN0WEtNM2RaaDBtZnNnVGZ0YzlkSnJJUUsrRTI0WHp2MklhRVJxZEhTS0lYT0s2N3F0b09NL2lrZUxvcDJKN0VjUCtDcVJOS2t0NzJyZ3E2ZGNaaVdGSGZNZmZMSUQrQlBlQWdLZDI0R1NjeUNJUTFoSFFldFlFaUVqdjd6dUZOVDBxTzVTY29nTkhiWVRVaEhrdjFVdlErSzdSeGNCMFhKbW1SYUNzUHhvc0NhQzJyVzZ4ZWNqdE9qd25wKzJNb3h6NGcyTE9BV2dMYVpzZktOYVl1WGozRWVMbGJUSm14Z053bUFWeUVWZUFvNXM5UW9OOHhBU0VHbWh3N1RoNWloallBZ0FIKzduSTNtbktBYTJUYldYSzZtRXc3NGxVQmI5MWpaMSt0UmR0QlZYTXZ3QW94TXQzdStmSmFNU3E4dHNWanVhcVl6dWJBTnQzWDBySGRIWVkiLCJtYWMiOiI2YThkOTNmYmNkZDUwMTFmMmEyZDhkN2MyMWM1ZmU0NWJmMzI5NWNjMjM2OWFiZjU1ZmU0YzEzNDg1MzVhYWY4IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:33:37 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlArUnF5QnhqUTdtYUs2K0FqU1pIQ2c9PSIsInZhbHVlIjoiaG1rbkl3MXJZdUFVakJUWHVWNmFzWVZadGczLy95VU5Db3psalNNMlRORzlVL3kxbE0wN0t4eUQvbkY4ZXR5QmxsalcwcFU4TEpHT0xpWi9hSHF4amVSK3FoemlacVhUOHpHK3JuVkpjYk5vYkxxNEFNZmNIaWUxQndJdEd5ZHY2eDdtZUVYem1RRnVxYzFDbHU3UVNpKzM5VW1udFBnMGY1TFhqcEpKM2oyTlEyZHBYTnptUG9TNitXNTZncGYwRW5qRnFubGh1RkVOMnNlQmdQY1p3dnhLK2s3TmJPV3hFVDFMdzRrcGlSeHY5RmtRai9HSWxtRHdiL3BPN0ExNDhzSkVTa3orWkdRV3dSNFhUUlZlanVnaWVCK0hHcGVnWEZJNjllUnFuckxoK2ZwYmdHR2x2ck5hak41aGpsUndyOXlHZFFnSmFkTGZVOHNQQ3lXNktjWDFUc1l1dHB6SWRjQ2JhYVY0RE9lWlpIcEppaHdZZnBHWTIxTjRWN2RCaWhPNDZkQXd2UW1sYjB3QVhGeHpOTXJaZFN6ZDYrNncvdHJwSWR2T2hoeDNVUU95TXpRZEVDZEkyNGFWdTFLRGhUYW9KYUNLSDNHb0xoMWMwcTF1dVV2VndQY3AvdEhtQkZYcWJnWk1MUzdnWGF1bzR5WTNWNExZeUxBNjhNeDYiLCJtYWMiOiIzZGZmZWZhMjM4ZDcyZGI4NTg5N2ViYjdkMDNhODkwZGFkNzM2ODhkNTlkMDViMDVhMjgxMjI3MjQ2NDdjZTMxIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:33:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjNRajV5ODVxREVBMmsySTJ3dWNDbkE9PSIsInZhbHVlIjoiZUE2WFppYnJrRDA0by9yeTRrZVNWU1dOeEJ4NXVNeGlVU2pvcEZaUkFVNFA1aWRlbk0wWXhIc29WVzVLSFprTERFQ1d0bExHckxqbmlJa0FJZ1NzQTdQdEdhUm1WcTJvdDVRcnJ4bmw1YThLSElBSE5xOTUyL3NEb0tnV0hSMHZ3ZksyNUlxS0VNWFh6QjVBOTQvZHlOR29NdUk3T0lCaEVyRmloc2RLTlN0WEtNM2RaaDBtZnNnVGZ0YzlkSnJJUUsrRTI0WHp2MklhRVJxZEhTS0lYT0s2N3F0b09NL2lrZUxvcDJKN0VjUCtDcVJOS2t0NzJyZ3E2ZGNaaVdGSGZNZmZMSUQrQlBlQWdLZDI0R1NjeUNJUTFoSFFldFlFaUVqdjd6dUZOVDBxTzVTY29nTkhiWVRVaEhrdjFVdlErSzdSeGNCMFhKbW1SYUNzUHhvc0NhQzJyVzZ4ZWNqdE9qd25wKzJNb3h6NGcyTE9BV2dMYVpzZktOYVl1WGozRWVMbGJUSm14Z053bUFWeUVWZUFvNXM5UW9OOHhBU0VHbWh3N1RoNWloallBZ0FIKzduSTNtbktBYTJUYldYSzZtRXc3NGxVQmI5MWpaMSt0UmR0QlZYTXZ3QW94TXQzdStmSmFNU3E4dHNWanVhcVl6dWJBTnQzWDBySGRIWVkiLCJtYWMiOiI2YThkOTNmYmNkZDUwMTFmMmEyZDhkN2MyMWM1ZmU0NWJmMzI5NWNjMjM2OWFiZjU1ZmU0YzEzNDg1MzVhYWY4IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:33:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2099447470\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1317375279 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/contact-groups</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1317375279\", {\"maxDepth\":0})</script>\n"}}