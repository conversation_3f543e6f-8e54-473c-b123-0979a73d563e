{"__meta": {"id": "Xc776b4cf1aa2b1de95dc42286c7c5f05", "datetime": "2025-07-29 06:38:22", "utime": **********.763828, "method": "PUT", "uri": "/contacts/leads/21", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753771101.948654, "end": **********.763849, "duration": 0.8151950836181641, "duration_str": "815ms", "measures": [{"label": "Booting", "start": 1753771101.948654, "relative_start": 0, "end": **********.650887, "relative_end": **********.650887, "duration": 0.702233076095581, "duration_str": "702ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.650901, "relative_start": 0.702247142791748, "end": **********.763851, "relative_end": 1.9073486328125e-06, "duration": 0.11294984817504883, "duration_str": "113ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50387376, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PUT contacts/leads/{id}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ContactController@updateLead", "namespace": null, "prefix": "", "where": [], "as": "contacts.leads.update", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=378\" onclick=\"\">app/Http/Controllers/ContactController.php:378-487</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00822, "accumulated_duration_str": "8.22ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7020898, "duration": 0.00553, "duration_str": "5.53ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 0, "width_percent": 67.275}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.720873, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 67.275, "width_percent": 20.925}, {"sql": "select * from `leads` where `id` = '21' and `created_by` = 84 limit 1", "type": "query", "params": [], "bindings": ["21", "84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 382}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.727246, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:382", "source": "app/Http/Controllers/ContactController.php:382", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=382", "ajax": false, "filename": "ContactController.php", "line": "382"}, "connection": "omx_sass_systam_db", "start_percent": 88.2, "width_percent": 11.8}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/contacts\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/contacts/leads/21", "status_code": "<pre class=sf-dump id=sf-dump-1620198255 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1620198255\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1463768409 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1463768409\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1046485964 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">PUT</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Gungun Rani</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"23 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"10 characters\">8697562305</span>\"\n  \"<span class=sf-dump-key>date_of_birth</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-07-29</span>\"\n  \"<span class=sf-dump-key>contact_type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Lead</span>\"\n  \"<span class=sf-dump-key>tags</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>postal_code</span>\" => \"<span class=sf-dump-str title=\"7 characters\">7733055</span>\"\n  \"<span class=sf-dump-key>city</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Siliguri</span>\"\n  \"<span class=sf-dump-key>state</span>\" => \"<span class=sf-dump-str title=\"11 characters\">West Bangel</span>\"\n  \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"5 characters\">India</span>\"\n  \"<span class=sf-dump-key>business_name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Smart Internz</span>\"\n  \"<span class=sf-dump-key>business_gst</span>\" => \"<span class=sf-dump-str title=\"12 characters\">GTP569865241</span>\"\n  \"<span class=sf-dump-key>business_state</span>\" => \"<span class=sf-dump-str title=\"11 characters\">West Bengal</span>\"\n  \"<span class=sf-dump-key>business_postal_code</span>\" => \"<span class=sf-dump-str title=\"14 characters\">895647895625HP</span>\"\n  \"<span class=sf-dump-key>business_address</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Siliguri West bengal</span>\"\n  \"<span class=sf-dump-key>dnd_emails</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n  \"<span class=sf-dump-key>dnd_whatsapp</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n  \"<span class=sf-dump-key>dnd_calls</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1046485964\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2181</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryQ1q3rYrxAqpsD78B</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IlNVTWY2bUIzZmgwUXBqSXFBTXVRZHc9PSIsInZhbHVlIjoiKzlZWkhwNzI4dFArcDRtd0VJTHh3UG5LWUUzSjhrSFA3d0ppOHJtRXMybXF3ekt1WmI2Rks4RUJUMmNkc0JGcWc1UysyMEJpNnV0aVpsY3lJaFVvVWl1czdrekVGajdFcFRFdEprcFB0MGZsSDFFRXplSmJnOUgwS2tyMThJTk5LY24zZk5LcWVoTUhocGZ2R2FJS0dFODhsTngxdytzMVpFV3AzNlRUY0hmMTY0MmFtMFJqMkJlYU9RSk94bkdIZGhzdWdqTTZqdnFXem9GcTJ2SWx4TE9qdGVpZ2h1VTMxa0JCOGNIUEFEa3dmNWhqWXdxSmw3b0trTTZqejNld0lkcWNPREFyZkRPNVIxZ1phbE5MbWJFdm1veHBCblpnU1pBUjN2STRDcjlabUVmK0pBc092MUorR0F2dExnUERxTkJObUFyTm0rY2taOHM5OE1VNm53Y0lscktpeG1BNi9ySUVrNGtCTmpJNUh2elJWeHdRandjMnRrSXlnZkdheDZFTUdTQkY4ay9MV3cyWVVTSmp3dm01c3hrTDZqSnhtQ3BGQllKRXlnQXZkZnFkK0c1ZDliOFlOOThrNkl4bTBsS3o4bkNFWGJRMGZvMElwTkUvYUpyZ1JpSHFjVGZsV2JPUThsTmN2V0U3bS80OXlBaE9NRzNhSHZxM0djTWYiLCJtYWMiOiI5YjkwZTM0NjFiNDIxYmU3NzQxZjJhOGJjMzYwMGMzYzI2N2FiOTVhYjE0OTY0YWE5MWNiZjJhOGMwMmMxMTVlIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IkZ2T1o5bEFsOUF1T0dRam5vYkhtdHc9PSIsInZhbHVlIjoiY1BkeEl3VTI0ZTRxTGQwV2hBZ25LQUxka2dpT09aTUs5TkJ6WEptYUY0UmovbXQ2T2xEQ0x1RVo2M0NxVXlvdDlZODhsdnM4cm5sU3NVSEJqZ1RMSDlQVitNTm9MNktDL0xadVNpS3hUSGdjQzhnNWdIVXU1RXlveDVCaE1YZ0JyS085eFZxdEdSQURobHEvb0JteFN0dVVYMS9HNG5JMTEyb0E4L2p5ZE1VVVRoeVFMZVZmOWtFU2t4K0cyZG5hbDBYOEkrVkIveTN4YTJNbmk5ZmdLeTRscEljNCs2QnoxS0JQYVppTmlHTkxkdWQ2Rk8wSjMzMWFSWk9rQlY0dDdBTEJ2WkhkdnBEUVp2Z3hQMXhOT2EzQXBLbThLQjVLNE5sajNBK1AwajBjcjVVYnFneTdXeFFZZ1NHSHBtOERvZkFuZEZXQjZ3RlRveFc5NDlrWWt6cG1UYTlZZ3dqSG8rT1k4UHhiczZtRHFWRlpSR3ZEMGYzMnRlVlFJTUtqd1VLd2pKWjlzeWI0c3k4MHE2KzNmeGU3cGtFNUpHSWM0VnAxUkRNbnpVNS9KZ05LQlJvdE9sR0h5V3l6cHdxYzdSRm9EczQ3bFB0SC9IV3FxTm96SVJXOUpvcS9WVjNrZmJCWjFDTDRqVS8yQ3Uva1NlQzFkWHpEVG9LR01qd0siLCJtYWMiOiIzMmFmYmRhMDg4OTMwZWMyNGNmY2Y1OTI2ZTcxMzQ4YjM2NmI5Yzk3NDEyYTUwZmQ1MDI1Yjg1MzFhYWUzMGY2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1XDrxTWmYv3gorVrXvmSodhuVHNiE4Wue14QMI4v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-858476232 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 06:38:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii84NWljVGFHandPK25ZVHdoanNLSkE9PSIsInZhbHVlIjoiYVlZb2dZWW9qREp6NEJKMVFycEtiTE1LT1VTOSt3Vlo5d254U0pKVmVUeFhpNmI2dm9tb0o5YTJDMVdjL3RIRGcyVEZtUk9LV0ZiK3NKSkpBTzAyZzNIdU4zVk9sRldYRXNaKzVqSUR6dmFaOFk3T2tHYTJkMHFkOG5UTSt3d0tOSnFKbEQwR0tuakpWTlU3amhtUDFuVVVGZTZ5UVIwNDNZVzhhQVVjSGpNbVpvZzVqOHB0aUtuMjhBSVNodkwyVU1qMjliSEd0L2ozU0d4MWtIc1d3cVByYjV1Q001R0YzSUtwZkdjM2MzWlh3d0VlSWVTUTNicDFVK3lSeDYxOENZWWRSSXhxSTVQT2JmZDhMeURHTEFYYkJGNjk4Y3BhU2J2YUpodTE5RlZHNGNlQW1ueU1DMHBKdlRLL1BkaWlCL2NNSzRzQkhQYWlreGI2ejVXeUdwV3BOOUkzOHc4OVE0aGNQeDF0UzJxZGZUR0hGdDFjSmovamF3Q1F3c1hqL21VM2hMbHUwb1B0clIvbzNiM0JHNzd5b1dLS3ZVUTRrbWZ6UnZSRCtBSFR3NEk3dzh4eGF5MzZjQytlc0p0WWNWbVNMems1WlFxWXFKdHpGMXhvOGRiMXJqV29KcS85UjVRZnZDRHJuYTB6cUt6bGo3ZXcrZmlTMHMwemJUaXAiLCJtYWMiOiI0NjZlNzcwNGE2Njk4NTAxY2FkNmQyZDI5MzE2ODY4ZDAwYjMyZTc2M2EwYTkwM2E5NWUwODIyM2EwZDJlZjA1IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:38:22 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlpSOHpIMGhDTzZiYzFHUVJlR1JUN0E9PSIsInZhbHVlIjoiNTRzT2pMOERqNVduajFObzRsbTNkSTV6NllxQldDZDBvVmZvZ3hoOEhLak5ROEJLOS9DNklRdzRKUWcxcllwbitZRk5JZWtOSXVqSGRuVjZDT0l3aWpva283eEc3SGF3RXExS2R0MkQyMUxUOGdyNGR3NjV2N2dvWFd1Z1BYRWxNdEpiOERzOFN2YWc1eHBRdHJxQ1IzSnZhSk1uN3Z2WmxKcUtEUUR4Nk92SjJBWjdOYkc2RExMQk1zZWI2RTNZdFloSjNKc2JiN0JuYlFkTjhUeVpIeHVqc2NRL3ZxdzF0TnNNQmlBSHluM3hURWUzS3pEcjMrQjlkY1ZETExwWUZPeXBSQXlMclR4dGdYYTI1ZEZlRDB1R3BEVGI0WXRVaVNCU3FyRnJVRlFyU1RJN1dENU5yWXFhOEFjTTNSQW1kQ1ZnZmVTT3RScWs2WHZHMExBT0xITmc0SEdqcDE3S2JqZU1CU0ZUS09LNWxodC85OE55VzlSYlprbEpBMWpHN1dmSGRwTFpFT0RpWEd5RU5WRXdYcnJlVGlGVjkwSE1sZ2R1K0FoRWRFUHYvMzd6K0xwZXpBUmhvNVFlWGx0UE9pa1FkcU5UeE9Qa0N4bTh6MVc0Q2l0QndrSERacjZnM3RoOGtjY0dCOThEMDJTOTZMK1VQUkMrM2FGckRnVmciLCJtYWMiOiI0MTZkYWQ5ZDQxMDFhNDcwMTM4MzcwMTI2MzAzNWFhYjc1ZTE4NDkyYjliZGNlMmUwMmEwMDljYzQ2ODRmYTRmIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:38:22 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii84NWljVGFHandPK25ZVHdoanNLSkE9PSIsInZhbHVlIjoiYVlZb2dZWW9qREp6NEJKMVFycEtiTE1LT1VTOSt3Vlo5d254U0pKVmVUeFhpNmI2dm9tb0o5YTJDMVdjL3RIRGcyVEZtUk9LV0ZiK3NKSkpBTzAyZzNIdU4zVk9sRldYRXNaKzVqSUR6dmFaOFk3T2tHYTJkMHFkOG5UTSt3d0tOSnFKbEQwR0tuakpWTlU3amhtUDFuVVVGZTZ5UVIwNDNZVzhhQVVjSGpNbVpvZzVqOHB0aUtuMjhBSVNodkwyVU1qMjliSEd0L2ozU0d4MWtIc1d3cVByYjV1Q001R0YzSUtwZkdjM2MzWlh3d0VlSWVTUTNicDFVK3lSeDYxOENZWWRSSXhxSTVQT2JmZDhMeURHTEFYYkJGNjk4Y3BhU2J2YUpodTE5RlZHNGNlQW1ueU1DMHBKdlRLL1BkaWlCL2NNSzRzQkhQYWlreGI2ejVXeUdwV3BOOUkzOHc4OVE0aGNQeDF0UzJxZGZUR0hGdDFjSmovamF3Q1F3c1hqL21VM2hMbHUwb1B0clIvbzNiM0JHNzd5b1dLS3ZVUTRrbWZ6UnZSRCtBSFR3NEk3dzh4eGF5MzZjQytlc0p0WWNWbVNMems1WlFxWXFKdHpGMXhvOGRiMXJqV29KcS85UjVRZnZDRHJuYTB6cUt6bGo3ZXcrZmlTMHMwemJUaXAiLCJtYWMiOiI0NjZlNzcwNGE2Njk4NTAxY2FkNmQyZDI5MzE2ODY4ZDAwYjMyZTc2M2EwYTkwM2E5NWUwODIyM2EwZDJlZjA1IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:38:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlpSOHpIMGhDTzZiYzFHUVJlR1JUN0E9PSIsInZhbHVlIjoiNTRzT2pMOERqNVduajFObzRsbTNkSTV6NllxQldDZDBvVmZvZ3hoOEhLak5ROEJLOS9DNklRdzRKUWcxcllwbitZRk5JZWtOSXVqSGRuVjZDT0l3aWpva283eEc3SGF3RXExS2R0MkQyMUxUOGdyNGR3NjV2N2dvWFd1Z1BYRWxNdEpiOERzOFN2YWc1eHBRdHJxQ1IzSnZhSk1uN3Z2WmxKcUtEUUR4Nk92SjJBWjdOYkc2RExMQk1zZWI2RTNZdFloSjNKc2JiN0JuYlFkTjhUeVpIeHVqc2NRL3ZxdzF0TnNNQmlBSHluM3hURWUzS3pEcjMrQjlkY1ZETExwWUZPeXBSQXlMclR4dGdYYTI1ZEZlRDB1R3BEVGI0WXRVaVNCU3FyRnJVRlFyU1RJN1dENU5yWXFhOEFjTTNSQW1kQ1ZnZmVTT3RScWs2WHZHMExBT0xITmc0SEdqcDE3S2JqZU1CU0ZUS09LNWxodC85OE55VzlSYlprbEpBMWpHN1dmSGRwTFpFT0RpWEd5RU5WRXdYcnJlVGlGVjkwSE1sZ2R1K0FoRWRFUHYvMzd6K0xwZXpBUmhvNVFlWGx0UE9pa1FkcU5UeE9Qa0N4bTh6MVc0Q2l0QndrSERacjZnM3RoOGtjY0dCOThEMDJTOTZMK1VQUkMrM2FGckRnVmciLCJtYWMiOiI0MTZkYWQ5ZDQxMDFhNDcwMTM4MzcwMTI2MzAzNWFhYjc1ZTE4NDkyYjliZGNlMmUwMmEwMDljYzQ2ODRmYTRmIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:38:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-858476232\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-991060924 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-991060924\", {\"maxDepth\":0})</script>\n"}}