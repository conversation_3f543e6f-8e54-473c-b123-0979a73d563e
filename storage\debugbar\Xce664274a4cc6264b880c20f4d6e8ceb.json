{"__meta": {"id": "Xce664274a4cc6264b880c20f4d6e8ceb", "datetime": "2025-07-29 06:32:57", "utime": **********.8188, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753770776.65635, "end": **********.818842, "duration": 1.162492036819458, "duration_str": "1.16s", "measures": [{"label": "Booting", "start": 1753770776.65635, "relative_start": 0, "end": **********.726311, "relative_end": **********.726311, "duration": 1.0699610710144043, "duration_str": "1.07s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.726326, "relative_start": 1.****************, "end": **********.818846, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "92.52ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2999\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1805 to 1811\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1805\" onclick=\"\">routes/web.php:1805-1811</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ezNWpXmU8eyWws20Ie7NBP76h3ykYuegzIVg79nx", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-1949648534 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1949648534\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-671854482 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-671854482\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2035749243 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2035749243\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-766050251 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-766050251\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-973443551 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-973443551\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1624533327 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 06:32:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImtKN2ZYRU5GZ2FvWXV5dGtVaUNwUlE9PSIsInZhbHVlIjoiT3A0Nk16RkxpcTZEb2Zwa1UzZVNTbnltYUh6ektTTi9RZUJMb2NId1NwWUdqOHJhRithSTQ4cWdRL0QraTAwdy8xa1JTQlo2M3BQYzBnejBJUTJTeUNvTWdGd05qYzRYRDRhLzBacC9KR3hOZWl6QzZzUE9Kd1N6L21sVlorMU9MTjdaUzIwajJTRHp2NVgrOHdIUnNkc2QyNVg0K2lBdGpMMTlWNnJvQVNYZXA3cVdSemQ1TkJWT0E4MGwzWHB2cWlXbVNMTi9ZYjBiOFF1NlhoZXdhZitYaXhHWlpHcDl2Q0ZTWXlBaW15RjdNc2RwWkUvNDVPRDFKU1hwYXU5RUw1aEFRdFNjNEtRRUR1OXNMZENZL0I1UDFLLzBXZkhjRWM3ZnFuSjlNT3psWjQ0NlBzekt1dWcxbkVybHBQa0VZM0FycXQxRCtWOWFmcUVZWjJTWVQ4MnhIVHdxZUdEbEhVUU04YkdXemRQK3VJenNJMkR4ek92QlNSanJaTFgrbTdkeUZwa2JOSENuSHJjeTVtSWlFWE1PSU9RdmpoSXY2bFBEVk9JOHNoRVhDV05YaCs1Tko4L2tNbGtUbDl3cVNOa3FGWVBzd2xYbnNiMmhUUHkxUlhGbFA4ZkwxRkpnbmxCYkdqL0IwQnVucmg3WjdVZ004WUsxanNMQnU1RTciLCJtYWMiOiJhMzRiMzk2YTZkMzY1M2NjZDk1YWMzOGYyYjhlNDc4Yjc2MGFjMGI2ZjYzNTljMmEzZWNkZTBiZjdjMTFjMzAxIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:32:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ik1udGEycTdybUJ5d01WSCtZNjNMeWc9PSIsInZhbHVlIjoidUwweGJPYmhDbHgySk5uSG8wQXZSTk15bFZ0d01uT0RjZWthcDBUYUI3OXVKVTUxVUpQcWFQRk1aN2VncHNOek9FM09Hdjh4RW5OdnBhaGtsWXIrTE1kUUFFSmdnVnZaUThGcGlyMDVyNlN2Q1lrWDByWHNCQXUzaHp2THlqM2NEM3JnOFh2N216eDFQQkdLcFUvSEhHYUFlYURNaTZFRkNkMTJqSkVJU3Y3VE9wSGduK3ppL1c4SnlNaHV2dzZWYmxsUC93M2YxSVVDYncwSmFadTQrT3lkYXZIamRqZC9keUNxM3BPRHBWdGhobk5TaXF2aXFXRW1TYTVNWnA5Umt6TlFuUTU4QVNoeWxiSlZFMmZxcHAyaEh3U25XbjZoZ2N3OVdFOFVWUWlvUWJQMXF3eFBIc213Vlo0SkhwMml3Um90WmFPa1ludVI2SzhQb1E1T3ZZVmhydEdLSEZEL01PSGxrV0lvcWNOQjcyK2lUaGFBbnEyWHo5eEtLa2hwZVA5TGQ1VUNFQlpyWlFTRmF2ZFJYZmM0N3RHQ0pDZjBsemFyUGlDMTZXQ0l0bC9xOWdVWEpaUXg3b2wyUW5TTXlhZ0hra0hmNjRFdTJDNm5DUUoraWtOY1Q5VkNSYjdvLy9NVWQ2b0xYU2lPOE1TUyt1LytueUxVNEpmZi8vTXgiLCJtYWMiOiI4OWZlY2Y0ZmViODU3ZGIxOGI0ZjY0MjllMjA3NjNkZDhjMjljOGIxMjE3NDE1NWZhODk2NDYwMDk3ZjQwMTA1IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:32:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImtKN2ZYRU5GZ2FvWXV5dGtVaUNwUlE9PSIsInZhbHVlIjoiT3A0Nk16RkxpcTZEb2Zwa1UzZVNTbnltYUh6ektTTi9RZUJMb2NId1NwWUdqOHJhRithSTQ4cWdRL0QraTAwdy8xa1JTQlo2M3BQYzBnejBJUTJTeUNvTWdGd05qYzRYRDRhLzBacC9KR3hOZWl6QzZzUE9Kd1N6L21sVlorMU9MTjdaUzIwajJTRHp2NVgrOHdIUnNkc2QyNVg0K2lBdGpMMTlWNnJvQVNYZXA3cVdSemQ1TkJWT0E4MGwzWHB2cWlXbVNMTi9ZYjBiOFF1NlhoZXdhZitYaXhHWlpHcDl2Q0ZTWXlBaW15RjdNc2RwWkUvNDVPRDFKU1hwYXU5RUw1aEFRdFNjNEtRRUR1OXNMZENZL0I1UDFLLzBXZkhjRWM3ZnFuSjlNT3psWjQ0NlBzekt1dWcxbkVybHBQa0VZM0FycXQxRCtWOWFmcUVZWjJTWVQ4MnhIVHdxZUdEbEhVUU04YkdXemRQK3VJenNJMkR4ek92QlNSanJaTFgrbTdkeUZwa2JOSENuSHJjeTVtSWlFWE1PSU9RdmpoSXY2bFBEVk9JOHNoRVhDV05YaCs1Tko4L2tNbGtUbDl3cVNOa3FGWVBzd2xYbnNiMmhUUHkxUlhGbFA4ZkwxRkpnbmxCYkdqL0IwQnVucmg3WjdVZ004WUsxanNMQnU1RTciLCJtYWMiOiJhMzRiMzk2YTZkMzY1M2NjZDk1YWMzOGYyYjhlNDc4Yjc2MGFjMGI2ZjYzNTljMmEzZWNkZTBiZjdjMTFjMzAxIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:32:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ik1udGEycTdybUJ5d01WSCtZNjNMeWc9PSIsInZhbHVlIjoidUwweGJPYmhDbHgySk5uSG8wQXZSTk15bFZ0d01uT0RjZWthcDBUYUI3OXVKVTUxVUpQcWFQRk1aN2VncHNOek9FM09Hdjh4RW5OdnBhaGtsWXIrTE1kUUFFSmdnVnZaUThGcGlyMDVyNlN2Q1lrWDByWHNCQXUzaHp2THlqM2NEM3JnOFh2N216eDFQQkdLcFUvSEhHYUFlYURNaTZFRkNkMTJqSkVJU3Y3VE9wSGduK3ppL1c4SnlNaHV2dzZWYmxsUC93M2YxSVVDYncwSmFadTQrT3lkYXZIamRqZC9keUNxM3BPRHBWdGhobk5TaXF2aXFXRW1TYTVNWnA5Umt6TlFuUTU4QVNoeWxiSlZFMmZxcHAyaEh3U25XbjZoZ2N3OVdFOFVWUWlvUWJQMXF3eFBIc213Vlo0SkhwMml3Um90WmFPa1ludVI2SzhQb1E1T3ZZVmhydEdLSEZEL01PSGxrV0lvcWNOQjcyK2lUaGFBbnEyWHo5eEtLa2hwZVA5TGQ1VUNFQlpyWlFTRmF2ZFJYZmM0N3RHQ0pDZjBsemFyUGlDMTZXQ0l0bC9xOWdVWEpaUXg3b2wyUW5TTXlhZ0hra0hmNjRFdTJDNm5DUUoraWtOY1Q5VkNSYjdvLy9NVWQ2b0xYU2lPOE1TUyt1LytueUxVNEpmZi8vTXgiLCJtYWMiOiI4OWZlY2Y0ZmViODU3ZGIxOGI0ZjY0MjllMjA3NjNkZDhjMjljOGIxMjE3NDE1NWZhODk2NDYwMDk3ZjQwMTA1IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:32:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1624533327\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ezNWpXmU8eyWws20Ie7NBP76h3ykYuegzIVg79nx</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}