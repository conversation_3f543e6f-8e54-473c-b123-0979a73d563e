{"__meta": {"id": "X4ea3572901deb05b2231bcdc8ebb539c", "datetime": "2025-07-29 06:32:06", "utime": **********.4351, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753770725.564424, "end": **********.435135, "duration": 0.8707108497619629, "duration_str": "871ms", "measures": [{"label": "Booting", "start": 1753770725.564424, "relative_start": 0, "end": **********.35825, "relative_end": **********.35825, "duration": 0.7938258647918701, "duration_str": "794ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.358283, "relative_start": 0.****************, "end": **********.435138, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "76.85ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2999\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1805 to 1811\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1805\" onclick=\"\">routes/web.php:1805-1811</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "yOHjBPwCmvX5Ado8mlHrRajfm15QTmwwbDbU7wtK", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-1760913026 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1760913026\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-777217684 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-777217684\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1347431724 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1347431724\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-774085738 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-774085738\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1313710249 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1313710249\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2122740471 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 06:32:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ilh1alh3N2NYSThLSzRoLzREcHE3emc9PSIsInZhbHVlIjoiYWpwY0hJa3VsNms3b3REMDhsd2NuQW9wbkZJaW9leWRKWFQwblRLemJIVlJ2RE45VzVxUFBzOVpzb1d2Z2JLQ0IyZXN1MU9QZFZPUG1FVGpwYXlQanBmemVkdzI2eUthZkszeGs3aDIwRFFSVy9PUTQwUStHZUpSWXR3dGlEYjJXUjcrVmtuQmFBQU9vT0NMSVpsWkpoTFE2d2dGZ2U1MnR4dmJJRVF2SDFXME80dm5BMzF1bjJCQjdyV3hVblJ1RkFXc3FpZG9CWUxaRzIwZE81NFRWeFVrNE42bEtpQXFJUTdSWnI1OWZnS1FndVJBVitmYUJzREUwMTVsMUtGUm9FQ0Jaclo2Z3NXdnM1aUJqZTBxUlgrNnE4ZHJHMzNTbzNyRlJ4WWE1dEpTRE9yMW9pVFBqTG5KK3RRbHlhQWJISmptMHFIWUtQWlFlWC9yRk1vZ3dCU1RNZjZBd3Axd3c1cGJITkRzMUdFTW1rYkhWU2c5ZkZJZEwwb1dCbENzVlFWZk11c0J6bnphMjFDWW04dmR4c2I2UkZ1TWlqUUdJM0w4WmhyNmtuQklEdTNVNkxqcE9TakNhTlVYMnkraVBmNklTVnZQV0E5UUsveFJFUHhHYjVWbzFVM2FqS3VPdDM5eEVFbnhNTGhLNFVyWHBKa09qbDZ2cncxMjFKY04iLCJtYWMiOiJiM2JiYTJiZjdiMzQxNWI0ZWIxMGE0MmJiYjI2ZWVmMmVhMTE2NDdiODBkOGM1Mzc5ZTdhNzQwNDkzM2IyOGQzIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:32:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkFHT0I2a3NzSkMyY2EwSnhtRWNHc3c9PSIsInZhbHVlIjoiQk1GOTdLaUN4NVlGRzZUWTlLd2ZObmQ0WXYzL056MWthbnFNOWxLQ2gxWEV5REdkMldQb0NBZG9TSG8yVzNzeDBsSGhYcGZlanlRM1hDSkFqR2hhZnNzUytxOHdOazlzZzJMSmlUcW9SUzBjTGRmWlhRYzFGemhkSkI0M0trMDZUSzk3NXJWYXBrTEFJMTdpVjZLL3VwSFBSY2hPUThXNEVwbjFKeVo2TkI1YXVLR1hnMGtyQW9CZTdUeHJMek1MS1BJMDdMSWlMQ0JWMTJOL3NXVkhCZEFWQTlrbDBhNitTZytRZ1NQQ1djTlNlUzhXZ2ppdTlMUnRkZDNHTGtHOWI0UWZxU1lkV1BqWjdsaytFMjc5SzV0ckZnOHJINStlR0p2WUNjaUk0aGJmaUE2NkpzcTJIS3hpSHRlN1JTWXowZUxWNmlrYmdyQ0E4c1l2WUZka3hnTjJ6MFRoMzV1NUxTeXNMNC96Qm5WWUNKdHV5NXh4dmI4QVpkV3VGREVxWml6ei9TM2ZuSmtxZjJlWTJSR25pSXVSTTdGUDBHWmV4UlVWVEdybG9lcEw0NzR5dmNFdGxERFN0QnY5Q3FUS3RRNkkwVUx2Mis0ZE5rT2pqTWR3c2Zzb045SnBrUkJYc0NQT2xkUFpHeHROdFNtaC8vbU55cmI0R3FlSlVDOGYiLCJtYWMiOiI5NTkxMzA4M2Y1YWU5NThhN2U4YmNmNzU1ODFjZWFhMzZmOGEyODIxYmJjNGFhOTg4NTM4ZWRjZWIxODMxYTEwIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:32:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ilh1alh3N2NYSThLSzRoLzREcHE3emc9PSIsInZhbHVlIjoiYWpwY0hJa3VsNms3b3REMDhsd2NuQW9wbkZJaW9leWRKWFQwblRLemJIVlJ2RE45VzVxUFBzOVpzb1d2Z2JLQ0IyZXN1MU9QZFZPUG1FVGpwYXlQanBmemVkdzI2eUthZkszeGs3aDIwRFFSVy9PUTQwUStHZUpSWXR3dGlEYjJXUjcrVmtuQmFBQU9vT0NMSVpsWkpoTFE2d2dGZ2U1MnR4dmJJRVF2SDFXME80dm5BMzF1bjJCQjdyV3hVblJ1RkFXc3FpZG9CWUxaRzIwZE81NFRWeFVrNE42bEtpQXFJUTdSWnI1OWZnS1FndVJBVitmYUJzREUwMTVsMUtGUm9FQ0Jaclo2Z3NXdnM1aUJqZTBxUlgrNnE4ZHJHMzNTbzNyRlJ4WWE1dEpTRE9yMW9pVFBqTG5KK3RRbHlhQWJISmptMHFIWUtQWlFlWC9yRk1vZ3dCU1RNZjZBd3Axd3c1cGJITkRzMUdFTW1rYkhWU2c5ZkZJZEwwb1dCbENzVlFWZk11c0J6bnphMjFDWW04dmR4c2I2UkZ1TWlqUUdJM0w4WmhyNmtuQklEdTNVNkxqcE9TakNhTlVYMnkraVBmNklTVnZQV0E5UUsveFJFUHhHYjVWbzFVM2FqS3VPdDM5eEVFbnhNTGhLNFVyWHBKa09qbDZ2cncxMjFKY04iLCJtYWMiOiJiM2JiYTJiZjdiMzQxNWI0ZWIxMGE0MmJiYjI2ZWVmMmVhMTE2NDdiODBkOGM1Mzc5ZTdhNzQwNDkzM2IyOGQzIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:32:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkFHT0I2a3NzSkMyY2EwSnhtRWNHc3c9PSIsInZhbHVlIjoiQk1GOTdLaUN4NVlGRzZUWTlLd2ZObmQ0WXYzL056MWthbnFNOWxLQ2gxWEV5REdkMldQb0NBZG9TSG8yVzNzeDBsSGhYcGZlanlRM1hDSkFqR2hhZnNzUytxOHdOazlzZzJMSmlUcW9SUzBjTGRmWlhRYzFGemhkSkI0M0trMDZUSzk3NXJWYXBrTEFJMTdpVjZLL3VwSFBSY2hPUThXNEVwbjFKeVo2TkI1YXVLR1hnMGtyQW9CZTdUeHJMek1MS1BJMDdMSWlMQ0JWMTJOL3NXVkhCZEFWQTlrbDBhNitTZytRZ1NQQ1djTlNlUzhXZ2ppdTlMUnRkZDNHTGtHOWI0UWZxU1lkV1BqWjdsaytFMjc5SzV0ckZnOHJINStlR0p2WUNjaUk0aGJmaUE2NkpzcTJIS3hpSHRlN1JTWXowZUxWNmlrYmdyQ0E4c1l2WUZka3hnTjJ6MFRoMzV1NUxTeXNMNC96Qm5WWUNKdHV5NXh4dmI4QVpkV3VGREVxWml6ei9TM2ZuSmtxZjJlWTJSR25pSXVSTTdGUDBHWmV4UlVWVEdybG9lcEw0NzR5dmNFdGxERFN0QnY5Q3FUS3RRNkkwVUx2Mis0ZE5rT2pqTWR3c2Zzb045SnBrUkJYc0NQT2xkUFpHeHROdFNtaC8vbU55cmI0R3FlSlVDOGYiLCJtYWMiOiI5NTkxMzA4M2Y1YWU5NThhN2U4YmNmNzU1ODFjZWFhMzZmOGEyODIxYmJjNGFhOTg4NTM4ZWRjZWIxODMxYTEwIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:32:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2122740471\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1439992652 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yOHjBPwCmvX5Ado8mlHrRajfm15QTmwwbDbU7wtK</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1439992652\", {\"maxDepth\":0})</script>\n"}}