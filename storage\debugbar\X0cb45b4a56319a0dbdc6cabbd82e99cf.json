{"__meta": {"id": "X0cb45b4a56319a0dbdc6cabbd82e99cf", "datetime": "2025-07-29 06:33:24", "utime": **********.519556, "method": "POST", "uri": "/contact-groups/8/attach-contacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[06:33:24] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 84,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/contact-groups\\/8\\/attach-contacts\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.515973, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753770803.49738, "end": **********.519593, "duration": 1.0222129821777344, "duration_str": "1.02s", "measures": [{"label": "Booting", "start": 1753770803.49738, "relative_start": 0, "end": **********.314536, "relative_end": **********.314536, "duration": 0.8171560764312744, "duration_str": "817ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.314555, "relative_start": 0.8171749114990234, "end": **********.519595, "relative_end": 1.9073486328125e-06, "duration": 0.20503997802734375, "duration_str": "205ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52076192, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST contact-groups/{id}/attach-contacts", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\ContactGroupController@attachContacts", "namespace": null, "prefix": "", "where": [], "as": "contact-groups.attach-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactGroupController.php&line=265\" onclick=\"\">app/Http/Controllers/ContactGroupController.php:265-323</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.036809999999999996, "accumulated_duration_str": "36.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.372663, "duration": 0.024399999999999998, "duration_str": "24.4ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 0, "width_percent": 66.286}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.41417, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 66.286, "width_percent": 2.309}, {"sql": "select * from `contact_groups` where `id` = '8' and `created_by` = 84 limit 1", "type": "query", "params": [], "bindings": ["8", "84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ContactGroupController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactGroupController.php", "line": 283}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4290972, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "ContactGroupController.php:283", "source": "app/Http/Controllers/ContactGroupController.php:283", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactGroupController.php&line=283", "ajax": false, "filename": "ContactGroupController.php", "line": "283"}, "connection": "omx_sass_systam_db", "start_percent": 68.595, "width_percent": 2.635}, {"sql": "select * from `leads` where `id` = '20' and `created_by` = 84 and `contact_group_id` is null limit 1", "type": "query", "params": [], "bindings": ["20", "84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ContactGroupController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactGroupController.php", "line": 297}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4343112, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "ContactGroupController.php:297", "source": "app/Http/Controllers/ContactGroupController.php:297", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactGroupController.php&line=297", "ajax": false, "filename": "ContactGroupController.php", "line": "297"}, "connection": "omx_sass_systam_db", "start_percent": 71.231, "width_percent": 2.173}, {"sql": "select * from `leads` where `id` = '19' and `created_by` = 84 and `contact_group_id` is null limit 1", "type": "query", "params": [], "bindings": ["19", "84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ContactGroupController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactGroupController.php", "line": 297}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4381912, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "ContactGroupController.php:297", "source": "app/Http/Controllers/ContactGroupController.php:297", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactGroupController.php&line=297", "ajax": false, "filename": "ContactGroupController.php", "line": "297"}, "connection": "omx_sass_systam_db", "start_percent": 73.404, "width_percent": 2.2}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.453101, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "omx_sass_systam_db", "start_percent": 75.604, "width_percent": 2.336}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (84) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.464654, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "omx_sass_systam_db", "start_percent": 77.941, "width_percent": 2.309}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (84) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.469387, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "omx_sass_systam_db", "start_percent": 80.25, "width_percent": 2.418}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.474521, "duration": 0.00638, "duration_str": "6.38ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "omx_sass_systam_db", "start_percent": 82.668, "width_percent": 17.332}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 543, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ContactGroup": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FContactGroup.php&line=1", "ajax": false, "filename": "ContactGroup.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 547, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/contact-groups\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/contact-groups/8/attach-contacts", "status_code": "<pre class=sf-dump id=sf-dump-100354527 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-100354527\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1556828242 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1556828242\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-302776574 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>contact_ids</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">20</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"2 characters\">19</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-302776574\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1862437078 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">89</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/contact-groups</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6InRWVUlQcVozYWIvZFROSTgyZkZrcWc9PSIsInZhbHVlIjoiUjRRVHJhTXQxQlEwc0Q1NkVuN0dUL292U0dHbkhuNTBTemNsakdsaUthZnJIL2NVdU10eVJEMldXZTBzWjRUejM0WCtOR1BEN2Q0N0lIWUV5cnlSbDlUcUFyUndKbzc5VkxLOTZNQ3FtR1FhNDc4VlFPY3VwTm1qYWhxMndaM0oyM1k3bC84RGlZNDB1MjNrQkI1MTRyVmpJcEN1QitIc05DRmI3TGtMOEF1YnlEcE5wdzhDcVBlWmFVcm5ERklyWW1PTjNadnRURGJIY3QwbXFaWUxZZk54Z3AyOFBkejJWaXBjL2RkTWpUcG9jaU5NR2xmS2IxK0ZPMGZZTTZxRGl4QjltaGd6Ti83WmFiYkk2ZTJ0SEhORW02YVVIYjB2bmxZUzFaSVUySUFaWmVtTWNKNnA2b0J2a0w2czd3aU8wWCtaMVVSUGxNMUxpYUQ0YmFpQVVWcmx3VFhvRkdWTCsxbGRRcStNbmlUTjQ1K0ZKQVN0TlNQc0M3bDdycU9wNG5qcmJCYlpObEpMZGVFQUJSMXhiMVpQcTc0elBFN2k0cVRGeFlXY0Z6NjQ4VGNqOVdPV05weVVYUy9vaTZMSWZmZnJNZndiQ2VqODc1Uk54VTd3bmNFZDNEeTA2SFluNlFDMVhkakREdWgzY3JLTEhuTStycTdmODBCZ3h6Z1UiLCJtYWMiOiJkZjU1ODk4OWZlZmE0OWJkMzliZDEyY2ZjMTVhNDRkNmI4MjQwZmZkZjBjZTNjMjVlMjBjNmEwM2UyOTVhOTY4IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IkxMWTV3WVB3WHhSWE1SblZ1WlpXU0E9PSIsInZhbHVlIjoiT0UyTjBoL0R1Y2c4Vk9Hbkl5MHF2Q2hSeW5ZL2IxdlhMb3IyMERhdU9xRHRoamg3QisrYVVJNjhjclNwY0VpRkxTbW84OEdaTUh2MHUzQ2JoQnJwMU1TSjljZWVXWCtwOGNRMFd4NERieTlGS0l3cXlMbXRFdGhUbjVIVFA4ZkpLU2JUWWcraWhKNmJWOWZWZ0dmRTFMU2FlZkxwY0VlV3NIVnhvYkc1UllnUW1Zb2MzbXJKRHNIL1AxNEoxcHkySld4ajR6ZHhpSGlKQ3NrNnV5YzFIRHJMVjg3dzFQejZYd0M5b2xvdThpczBuMUdLTmw0akx2NmdCRlBCOXJSTFo5c3Biall5OVBIWmQvcmlJTXF1ejREYzdOVXFXNGM1Y1lRSFVIMVNMMVBLSXFIQWpBZno2L0xrSTR1MnRQWXl5enNoZzY2RUkyNEZEWWdvNWZxb2FMK01VOWN6TWZOeTlhYXp2UlJseE1OZk85ckVLQ3JyTEFsdlJYS2l6NW5ERjBkYlZPcDJpNjMrSjEvUlVwMkc4S0FUSk43d0Noem9rMUVham54bzFscWoveHV2ckh4K1pVRlRFc09NMkx4akp5VFF4MzR2djNDMno4ejFoVkx0ZVR6aEVWUkZiRi9CTW5sR2lRdUdaelRFeml6NEttZGxqaGtFb1dqMzY0cUQiLCJtYWMiOiI1ZGU3MTczZTgwYWYxYWZkNTA5ZTkyOGI0OWI5OTEyMWQ4YzU0YTZlOGIxNzBjYjA0MzBkZmRjZTFlYmQ1ZjZhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1862437078\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1434996546 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1XDrxTWmYv3gorVrXvmSodhuVHNiE4Wue14QMI4v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1434996546\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1161653825 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 06:33:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjBudWE1c0oxSVZWTkxzd1JyZzBmYmc9PSIsInZhbHVlIjoiQWVkbWNPV0pXRVRoNEROWGw5OFdTTGo3OHlFZDFpblpNNmszMDM3c2RGSTdVeEtHWjFFdlU1a0poeWJueUtNaXlWZDZCM3c2QUNBTXk3dGZ6QjZLdXlYVk5tUXVHWE5hbmZ0VjFJcFZaK2sycm1zVHQrSnFxSGNZZ09HdFlaaXdQZE9jT0JzcnM4T1l6Z3laMlFuMndabW9FMkFXYU9ibS8xU0paYjM0cEJOVVA1S1RNWDRIcC8vcW4xdUZ5N1d1V3lDU2ErK2diUVRwTFo5SzlodjRCVWxSRGdZZ3VRdmRGM1VuUy9xdXliOGI4TG82V2s5NDVIbnFMODVrN3NLaXVuK2p2eVYwZUhidzRBNDNidjUrMEp4dndySGIrR2Z2WWRtQWZvenJ1ajR4RnFHUWhuSXdkQVZiejR1U3RVTVo2Tmc2TzI5enN2RWJ1NFJTNGxLNFgrVUJWWkxnZ0podnRmWTVHU3YxMEt2M1ZTU2w5a0c3T2JLQzQxODlMVWlYMWh2RzJmRjVzQVlFL2lRTmJ6TWdMVnpuRVBTcWlwN2pxUGF0RUp5bnArR2s0NE9INnZEclFlem45a3JOenY3NUhvb0dTM3JSTnY1T3c0TVhNV3Jia2F1c0VrZVN3V1NHVlFBbHJnT0hmQWdRdGlNamhwcTd6VXljcUF0WU5wYS8iLCJtYWMiOiIwNmE5ZDRkZmRkNTU3ZDFkMjQwMmQxOWQ2YmU4Y2FmMTE1NThkYjRlMmM4YWFkYThkNTlhOGYzOTZmYTQyYzZiIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:33:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjlmSUUwWmEzUFpJUVdjUlgyWU9ka0E9PSIsInZhbHVlIjoibHZvdjRucDFTc1QzN2hJRXRmdnlEQ2NPMVh6bmxNa3pGeitiLys3a3J6VHpjNGduYlNLakdmQXlmRWVoWHVKYWhLRW5UWTF4UjBjTWREcUZsNXd5bC9XMmVQWEF1d0xSc1BwWXZwbVRhYWhZYUNXWG84MWo0WCtFZEpCNjcvL0pnSlhJNnZsWVA1UHpwRks0N2l1eXBOWk8zZUlxZWxmcnFmZkk0b0xkY0V3Z0pOWTlWd1UvdDBJWU9EbjNTbVUwMmQ2bzhvTzJvZFovUW4vSmNpcnNIQUVJSTlZay9SQkdHTVVlU3lYT1o0WVV4YlZ2TWxJWjFUMWI0eVBxUGtUOGNJYm9WZG84cUJNMGpURG4yM05qbXNNU2U1c1JWVFlHeXpWTlZlSFU2Z3lLSGxwQnVRUk5nYjhEWWo4L3pSdFdGM2JxZmhMWlhIcm9vSTNObTlzd1dvZ2pYMXJmZFZpNlVkYVhVVEIvYzBjU1o5TzkvN0hEeVArUVo5UGVCaHRHc0JGQVBuUVZYZEQvR2lQcEg5RnNsM2pLRzR2Z01XRmVHNnVvZHdGTE13RW1sRzlBTW1UL0ZnRnF3QXhvdHR2TDRyd0ZxeUdaS0loblBhUDlRYlhnWXBIeTl5ajc2R0kvSFNtcDA2TWYwa3plSHF6Vk9NaE9kVTl5SkMyNUQ5ejUiLCJtYWMiOiJkOWMyNGQzMzFlNjEwOTY4ZTE1OWY4Yzk4ZmQ0MTljZmIyYzA3ODAyMTkwYTk1OWI2ZjQ2ZDVjYjBkMGJhZjU4IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:33:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjBudWE1c0oxSVZWTkxzd1JyZzBmYmc9PSIsInZhbHVlIjoiQWVkbWNPV0pXRVRoNEROWGw5OFdTTGo3OHlFZDFpblpNNmszMDM3c2RGSTdVeEtHWjFFdlU1a0poeWJueUtNaXlWZDZCM3c2QUNBTXk3dGZ6QjZLdXlYVk5tUXVHWE5hbmZ0VjFJcFZaK2sycm1zVHQrSnFxSGNZZ09HdFlaaXdQZE9jT0JzcnM4T1l6Z3laMlFuMndabW9FMkFXYU9ibS8xU0paYjM0cEJOVVA1S1RNWDRIcC8vcW4xdUZ5N1d1V3lDU2ErK2diUVRwTFo5SzlodjRCVWxSRGdZZ3VRdmRGM1VuUy9xdXliOGI4TG82V2s5NDVIbnFMODVrN3NLaXVuK2p2eVYwZUhidzRBNDNidjUrMEp4dndySGIrR2Z2WWRtQWZvenJ1ajR4RnFHUWhuSXdkQVZiejR1U3RVTVo2Tmc2TzI5enN2RWJ1NFJTNGxLNFgrVUJWWkxnZ0podnRmWTVHU3YxMEt2M1ZTU2w5a0c3T2JLQzQxODlMVWlYMWh2RzJmRjVzQVlFL2lRTmJ6TWdMVnpuRVBTcWlwN2pxUGF0RUp5bnArR2s0NE9INnZEclFlem45a3JOenY3NUhvb0dTM3JSTnY1T3c0TVhNV3Jia2F1c0VrZVN3V1NHVlFBbHJnT0hmQWdRdGlNamhwcTd6VXljcUF0WU5wYS8iLCJtYWMiOiIwNmE5ZDRkZmRkNTU3ZDFkMjQwMmQxOWQ2YmU4Y2FmMTE1NThkYjRlMmM4YWFkYThkNTlhOGYzOTZmYTQyYzZiIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:33:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjlmSUUwWmEzUFpJUVdjUlgyWU9ka0E9PSIsInZhbHVlIjoibHZvdjRucDFTc1QzN2hJRXRmdnlEQ2NPMVh6bmxNa3pGeitiLys3a3J6VHpjNGduYlNLakdmQXlmRWVoWHVKYWhLRW5UWTF4UjBjTWREcUZsNXd5bC9XMmVQWEF1d0xSc1BwWXZwbVRhYWhZYUNXWG84MWo0WCtFZEpCNjcvL0pnSlhJNnZsWVA1UHpwRks0N2l1eXBOWk8zZUlxZWxmcnFmZkk0b0xkY0V3Z0pOWTlWd1UvdDBJWU9EbjNTbVUwMmQ2bzhvTzJvZFovUW4vSmNpcnNIQUVJSTlZay9SQkdHTVVlU3lYT1o0WVV4YlZ2TWxJWjFUMWI0eVBxUGtUOGNJYm9WZG84cUJNMGpURG4yM05qbXNNU2U1c1JWVFlHeXpWTlZlSFU2Z3lLSGxwQnVRUk5nYjhEWWo4L3pSdFdGM2JxZmhMWlhIcm9vSTNObTlzd1dvZ2pYMXJmZFZpNlVkYVhVVEIvYzBjU1o5TzkvN0hEeVArUVo5UGVCaHRHc0JGQVBuUVZYZEQvR2lQcEg5RnNsM2pLRzR2Z01XRmVHNnVvZHdGTE13RW1sRzlBTW1UL0ZnRnF3QXhvdHR2TDRyd0ZxeUdaS0loblBhUDlRYlhnWXBIeTl5ajc2R0kvSFNtcDA2TWYwa3plSHF6Vk9NaE9kVTl5SkMyNUQ5ejUiLCJtYWMiOiJkOWMyNGQzMzFlNjEwOTY4ZTE1OWY4Yzk4ZmQ0MTljZmIyYzA3ODAyMTkwYTk1OWI2ZjQ2ZDVjYjBkMGJhZjU4IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:33:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1161653825\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-837795747 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/contact-groups</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-837795747\", {\"maxDepth\":0})</script>\n"}}