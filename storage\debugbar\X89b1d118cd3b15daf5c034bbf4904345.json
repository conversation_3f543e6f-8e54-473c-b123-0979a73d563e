{"__meta": {"id": "X89b1d118cd3b15daf5c034bbf4904345", "datetime": "2025-07-29 06:40:22", "utime": **********.586364, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753771221.603821, "end": **********.586429, "duration": 0.9826080799102783, "duration_str": "983ms", "measures": [{"label": "Booting", "start": 1753771221.603821, "relative_start": 0, "end": **********.516479, "relative_end": **********.516479, "duration": 0.9126579761505127, "duration_str": "913ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.516494, "relative_start": 0.****************, "end": **********.586439, "relative_end": 9.775161743164062e-06, "duration": 0.*****************, "duration_str": "69.94ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2999\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1805 to 1811\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1805\" onclick=\"\">routes/web.php:1805-1811</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "BRcNb1qZB3ACTixhSrBxrlyyaWrut8BKe9qydaep", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-2103416764 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2103416764\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1727823954 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1727823954\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-856601481 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-856601481\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-311071144 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-311071144\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1552587864 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1552587864\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 06:40:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkZCMFlxOENTeUFzZTFPemdaQXo3NVE9PSIsInZhbHVlIjoiNERNLy9qVHpTY0M1RUlzUDZuMWJxeWVOQlpxcGtQd2R4R0ZzZVNqcVYyRkJFTVdYNEV3QVpsZXRGcUN5T1RLTjdQQXRTeHFhRXYzMXUzVUJYYUYzc1ZLSEpFZVZJTStnWXhNaXlzQS9mQ0dlK1U0aXJVTmhLMmdYcUZSL1I3eFBKUmJ3bS9KR29zUjY0Y0JPeFUrQmtibU5WWk4zSzlwUUdsbk8ySWVKTTVNV25yS2wwcFJJSDN2RGdiZzVpeVh3dXBxMnJmK0J0aVFta1l1V0dHa2dLTTNYeGpLVnZzMmR5eHArNUlyZkxyVWlRUm91UUxybkV5SVNVTDFlbkluZ2lzaHZhUm1vd0RkTzhNZTJDWFgvMGxnbFdSa0hqS25lOWRRaWNLaTlpbG41bDR2cVJYL2pXR255RkNBRUpHT05NdmgvTXhpZk1tbERGQ2IxeXhpRmVIcE9pNjNNRWQwU1BpNGVRemVHQWN3LzJlRWpWSjd2bGp4QVZacEU3N1QyUHhPcVBKRnlCWXlLRksyNzE5VEhZejhCWVhINEMvWTluNnRHaCsrU2crZXRhY1NscGY5dWhVMGJWR0pxR2JrV091QVh0UGJkWHBERVA5MzZxUExmYzE1c3EwaDllMm5WL2ZjMGVzVUI3UEp1d3M0RnZWUHpaNDVkdVo3L2JDeUsiLCJtYWMiOiJlZjQ4ZDJiNDkxMTQ2OGE0NzI2ZjFmOWRhMzBmOTgzZTg3NGQ2ZDM4MmQ0MTNjOGE2MDYxZmVmOGRjMjczODk0IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:40:22 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkJFWU1ibDAzUFAyNGEyZTRaRVNKQlE9PSIsInZhbHVlIjoiektmMUREN25TMmtHU0tnU1ZtczR0eTNmWmE3Rlc2a0svM3ZvZVlwQThUeGtDbkpJVDNudTZQbjlLaFBERFFMczhKNzJLSnJ1VnMyd3Rhb1JraXRTTjgyZDZzaUlyZloxQnRJSERIZnRhcjdSMHVBbTNkUjI3aVdTVzBNUTZhdzl1elYxR2NjTzBYSjdnOE0vemNpY3lNMHNpdWhtM0lYS3dhSXNacTVUTlpxc0pDQS9QZVIrTHFsNCtxL0oybjI4UTJJemdURXpBckxmT0toeGlZQ0Fxa2QvOHptYmdLZDNhM2MyRjRMN0RLeGxIc0c2aEdBaUREWnpJWmdtOWhMdXcrV1kvSFRzaFE3RUlBUmZ6N2l2UU5DeVYzV2RTWkxYUW5IWWQxN0pqalU1eUQvaTFPTDk0NDVjMEVMSnpzYVNGdDJhcU5xUHFwNUdtcXorUG1TUzZ5b3NSZUZsUGxjL1pDeHlZaG1mWTdQSEhuN0QxNXd4TEp6eVlTZFVjQUt1bE1rS0RwU05tUDBCOUYzRUF1ZXQrelVIQ1lOZW9BaVFmdlYrNmJ2MGt1eEJ4YUFBQkdHaE5MWjROK21NMXNPd3pycUlmcll5L0lNM2U0ZzhITTk5c3BueTFTeEZQS3ZFVHpDc21IczF3Kzg3MWJpbjFyWXU5STJmUmk3QnRmTjQiLCJtYWMiOiJhNmViNzZlMTNkYzQ2YmRmN2YzZTBlNTA1YTFmOThiYjAyMjdmYzg0ZmYxNjNkZWNlNWNjY2RkNTE3NzI1NTUxIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:40:22 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkZCMFlxOENTeUFzZTFPemdaQXo3NVE9PSIsInZhbHVlIjoiNERNLy9qVHpTY0M1RUlzUDZuMWJxeWVOQlpxcGtQd2R4R0ZzZVNqcVYyRkJFTVdYNEV3QVpsZXRGcUN5T1RLTjdQQXRTeHFhRXYzMXUzVUJYYUYzc1ZLSEpFZVZJTStnWXhNaXlzQS9mQ0dlK1U0aXJVTmhLMmdYcUZSL1I3eFBKUmJ3bS9KR29zUjY0Y0JPeFUrQmtibU5WWk4zSzlwUUdsbk8ySWVKTTVNV25yS2wwcFJJSDN2RGdiZzVpeVh3dXBxMnJmK0J0aVFta1l1V0dHa2dLTTNYeGpLVnZzMmR5eHArNUlyZkxyVWlRUm91UUxybkV5SVNVTDFlbkluZ2lzaHZhUm1vd0RkTzhNZTJDWFgvMGxnbFdSa0hqS25lOWRRaWNLaTlpbG41bDR2cVJYL2pXR255RkNBRUpHT05NdmgvTXhpZk1tbERGQ2IxeXhpRmVIcE9pNjNNRWQwU1BpNGVRemVHQWN3LzJlRWpWSjd2bGp4QVZacEU3N1QyUHhPcVBKRnlCWXlLRksyNzE5VEhZejhCWVhINEMvWTluNnRHaCsrU2crZXRhY1NscGY5dWhVMGJWR0pxR2JrV091QVh0UGJkWHBERVA5MzZxUExmYzE1c3EwaDllMm5WL2ZjMGVzVUI3UEp1d3M0RnZWUHpaNDVkdVo3L2JDeUsiLCJtYWMiOiJlZjQ4ZDJiNDkxMTQ2OGE0NzI2ZjFmOWRhMzBmOTgzZTg3NGQ2ZDM4MmQ0MTNjOGE2MDYxZmVmOGRjMjczODk0IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:40:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkJFWU1ibDAzUFAyNGEyZTRaRVNKQlE9PSIsInZhbHVlIjoiektmMUREN25TMmtHU0tnU1ZtczR0eTNmWmE3Rlc2a0svM3ZvZVlwQThUeGtDbkpJVDNudTZQbjlLaFBERFFMczhKNzJLSnJ1VnMyd3Rhb1JraXRTTjgyZDZzaUlyZloxQnRJSERIZnRhcjdSMHVBbTNkUjI3aVdTVzBNUTZhdzl1elYxR2NjTzBYSjdnOE0vemNpY3lNMHNpdWhtM0lYS3dhSXNacTVUTlpxc0pDQS9QZVIrTHFsNCtxL0oybjI4UTJJemdURXpBckxmT0toeGlZQ0Fxa2QvOHptYmdLZDNhM2MyRjRMN0RLeGxIc0c2aEdBaUREWnpJWmdtOWhMdXcrV1kvSFRzaFE3RUlBUmZ6N2l2UU5DeVYzV2RTWkxYUW5IWWQxN0pqalU1eUQvaTFPTDk0NDVjMEVMSnpzYVNGdDJhcU5xUHFwNUdtcXorUG1TUzZ5b3NSZUZsUGxjL1pDeHlZaG1mWTdQSEhuN0QxNXd4TEp6eVlTZFVjQUt1bE1rS0RwU05tUDBCOUYzRUF1ZXQrelVIQ1lOZW9BaVFmdlYrNmJ2MGt1eEJ4YUFBQkdHaE5MWjROK21NMXNPd3pycUlmcll5L0lNM2U0ZzhITTk5c3BueTFTeEZQS3ZFVHpDc21IczF3Kzg3MWJpbjFyWXU5STJmUmk3QnRmTjQiLCJtYWMiOiJhNmViNzZlMTNkYzQ2YmRmN2YzZTBlNTA1YTFmOThiYjAyMjdmYzg0ZmYxNjNkZWNlNWNjY2RkNTE3NzI1NTUxIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:40:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1672689514 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BRcNb1qZB3ACTixhSrBxrlyyaWrut8BKe9qydaep</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1672689514\", {\"maxDepth\":0})</script>\n"}}