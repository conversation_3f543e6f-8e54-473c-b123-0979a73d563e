{"__meta": {"id": "X139ff900fc17c9ff72229ac2270b084b", "datetime": "2025-07-29 06:37:43", "utime": **********.473054, "method": "PUT", "uri": "/contacts/leads/21", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753771062.625251, "end": **********.473084, "duration": 0.8478329181671143, "duration_str": "848ms", "measures": [{"label": "Booting", "start": 1753771062.625251, "relative_start": 0, "end": **********.350632, "relative_end": **********.350632, "duration": 0.7253808975219727, "duration_str": "725ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.350652, "relative_start": 0.7254009246826172, "end": **********.473086, "relative_end": 2.1457672119140625e-06, "duration": 0.12243413925170898, "duration_str": "122ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50387376, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PUT contacts/leads/{id}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ContactController@updateLead", "namespace": null, "prefix": "", "where": [], "as": "contacts.leads.update", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=378\" onclick=\"\">app/Http/Controllers/ContactController.php:378-487</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.015390000000000001, "accumulated_duration_str": "15.39ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.404521, "duration": 0.01251, "duration_str": "12.51ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 0, "width_percent": 81.287}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4304051, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 81.287, "width_percent": 12.216}, {"sql": "select * from `leads` where `id` = '21' and `created_by` = 84 limit 1", "type": "query", "params": [], "bindings": ["21", "84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 382}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.436553, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ContactController.php:382", "source": "app/Http/Controllers/ContactController.php:382", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=382", "ajax": false, "filename": "ContactController.php", "line": "382"}, "connection": "omx_sass_systam_db", "start_percent": 93.502, "width_percent": 6.498}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/contacts\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/contacts/leads/21", "status_code": "<pre class=sf-dump id=sf-dump-777139483 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-777139483\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1210421541 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1210421541\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1377729024 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">PUT</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Gungun Rani</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"23 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"10 characters\">8697562305</span>\"\n  \"<span class=sf-dump-key>date_of_birth</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-07-29</span>\"\n  \"<span class=sf-dump-key>contact_type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Lead</span>\"\n  \"<span class=sf-dump-key>tags</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>postal_code</span>\" => \"<span class=sf-dump-str title=\"7 characters\">7733055</span>\"\n  \"<span class=sf-dump-key>city</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Siliguri</span>\"\n  \"<span class=sf-dump-key>state</span>\" => \"<span class=sf-dump-str title=\"11 characters\">West Bangel</span>\"\n  \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"5 characters\">India</span>\"\n  \"<span class=sf-dump-key>business_name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Smart Internz</span>\"\n  \"<span class=sf-dump-key>business_gst</span>\" => \"<span class=sf-dump-str title=\"12 characters\">GTP569865241</span>\"\n  \"<span class=sf-dump-key>business_state</span>\" => \"<span class=sf-dump-str title=\"11 characters\">West Bengal</span>\"\n  \"<span class=sf-dump-key>business_postal_code</span>\" => \"<span class=sf-dump-str title=\"14 characters\">895647895625HP</span>\"\n  \"<span class=sf-dump-key>business_address</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Siliguri West bengal</span>\"\n  \"<span class=sf-dump-key>dnd_emails</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n  \"<span class=sf-dump-key>dnd_sms</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n  \"<span class=sf-dump-key>dnd_calls</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1377729024\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1797923911 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2176</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryyllMSYBbWiFdX9ry</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6ImRtWHhwUWxaN0k1OGlWMVpRYkh3TkE9PSIsInZhbHVlIjoiY0djUjJUQWNsenJRSzI0bE9IcWk0N0NwUHhUTlF1V0RsUkUzVFd0VlMzb3ZaWlAzQnVWU2ozVmJtNFBrVk1NR3N0SllwMDc5N3M4cXFDK1FxY2JkNzNkWXFVakEzVk00Mk5YcnpOK1NZeDNqendpbDEzNWp1enkyUFUrcEdYM3IwR2dCeTgwSVBuTHowb3EwRlBhZkNqaTJRcFlPUkRFelBVenJpUnpHTEFoU09pUWs4OXc0SVFBN3IzVk1PQmVxcnE1ZnBYWEhuand4ZlpEdTFlays3UWhFYzE5ODBNVFRQUVhpaWNFa3ViR2tMMGZBZC9UYVlSTTN1b3RuREphTk9pYzVQeURudjNZK05GRmZYZy8yN2RWMVRQUU9DRWVGS3BGUGFPZjFLUDVoQVR4QkVRUkpYTWRWWUVLdGdGcHk4MnFSWnN2MDdLeHQ5eWYyUjZWb1kyeFZrbXhMRnZ1NE0rSEZKQ3RhaVNwSTdXOGkrOWJWQjU3bjM2UnZReTRmT0FsRkE5WnNTYnJxTWs5MURTTU4zMUFTYkdmcHNlWmpqVkFtRjk0WldTeTArYVQ2OVJEdzIrd2NnS0QvUGZRMENlTHBTSHBVOEJxTWxrR1JIUnV0a0U2b0h5RGZGd3dxdWc3L00xdThhNzZFbVZTalVVdXR1L1NabnhFMWVSNUciLCJtYWMiOiIzOTNmODk2ZTBiMTg2NWJjYzU4MGFhZDM4ZmI4YmE5NGE0ZWVlOGQ0YjFkMzJjYzQ4YmYxMzIwZTVhNTgxYWY5IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlkxMkxrNHI2ZU9FUXp0SDFZZm1GcFE9PSIsInZhbHVlIjoiWmI5QU9lVjZUTE9ZM2pNMG4xcU12SldrRHZHTi9RUGJrSkMxb2Noa1c0U3BRTFc5amNWQW0wb2ZGOWwyM0dMbE1sMEdUV1BrVG91ay9yTC9VdWI2bmt3REdtOVRkb2hsc3pPS1UvREVYa3RkUnlxVjhLYjVFK1pDYWlsUFBhdFRJOXUyaytZemllSFlUWU9zcUloS2VESHpHTVZXUDZGTVl3U3hWWW1JSWxqMkFURXgrRHVVUkhZL1pHSVUxSXdWZ3g4bzVNZnM5S0NwT0VSTUpxNFo3Uk93RXNwTGx5enZuYnB6WDhlbGR4S2xpcHIwQWNDMVc5d0pwNk9BTzFjN1FkVm1hUFdwUTJ1clMxN1VJY2MwaHVwRDZSR3M0T2FGc0IxMy9xVHdmL3NsTWlRMU9SZkI5aDJta1VHcy9sdnExSDhXU2JPcDRlMUlyVEs3VmgzVGZlbzBGL3FVMk0zSG5sYm5pRkhhZTJLQzd2WEpFRjROYXRYbjFVSlRRMGYvUU56WHNOZFEvbkthVElFazd1dUlBV2pKSkV6RmcvNTZhUlpjZEcrK3ZpcHBrcGNuc1NiQks5WnVxWldSYVM4NXFBcE9OL3R3U0hTNkhUN3ByNC9Md21CSHI3a015cTZFSUhzU0o1V1lqbDBFZWdVTFN5bTJFVzl3L0VyQUdHaFkiLCJtYWMiOiJhNTA2MmFlYmRiZjgxNGJjZTkwMGJlOGRkN2Y4NWI0MmIwMmVkNDA3NzRmMDU0YzdjOTAzNzk2NGEzZDdmNTIwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1797923911\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-82113751 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1XDrxTWmYv3gorVrXvmSodhuVHNiE4Wue14QMI4v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-82113751\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 06:37:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImRtV2RPRXoweHM2Z05UaFlsczRPL3c9PSIsInZhbHVlIjoidytIQXdEdmthZmFXTlpnbXp6REpXTkFMa1ZJRXo0anJSdDZrbXNQaWJYcjgwMHVuSmNQQjlWZFQwdGRPL0tsOTZjVW5Sa3htVWh0L1lVWUJCRlZSMks4QnZ2Vy90dm9WTTVxM1lCOCt4RHhuejBmbXFSVkRVN2JRczNKdWVrd2dpRGF5WTJqekd3Q25WRldtaER4SXNnNXdFN3pKUnhJT1B1VDFHMVN1MzVzYmZIbkNXR3oyZk9BS0tnSDlzY1UvTm1XZmYvUm1heHlibWxiMzZZa0xBaElIZlVCQnM5VWFzNmtZZzlCNDFyRVhPZjk0ajhKci9WcnZsMEJHWDlWSEE2UDU1akx5UEgyTWZDdWNINk5ZODNIQ1ZvVmx1amZoQzlhQy9kQkNOVnJPYU1zRE0wMFdpYzFIQTRvM2xNOXJFclJ1VG5paDJ2TGFSK3ZDbDhMcThIR3g4QXQyczFXTTNoazc2WHkwQWZUbzJJNTdEVzEvK3lyQjJBVkFORHlhTVBtbnliNnhQdXdXalJQTTJidkJFTlJjTDg4UGVKTk42Vm1mVmQvRjh2U0dPbkdOeTJTK01XcElPMkRkZTkrWWpJamRRMHpFSFo3MFNWS1JaKzMwR1dZUm5HV0lteFpDbDFkUFhRSHJaWnpLRzVYSGdmRzUrUHZKeUo0QzJwWU8iLCJtYWMiOiI0MmRlOTA2MTMyYTllZDdiYTQxNmJlNTRmNjM3NWM4NGU3N2YyZjA3OTE5NDQzZjE4NTFlOTAyODU1ODE5NzI3IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:37:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ik9oZTVKMld1Y1QrL2V6d2xDOFpPbnc9PSIsInZhbHVlIjoiMElUZjJTcW5aZXRncitXaUVzVE9SalZ0bHd3UHpTV0l4L2NnSU9Fc3pMWFlSaDVSK1FNbXpoMHN2a3M0d081dlFYdkFPRm9jNlRaYllGYzUvZnpPTmZxc3NmQmpqclg2RTRiMW5XM1RCUXhXVXorRjVybVBQeHQ2SHdBSEVmQnpyb3Fpa1VHMlFESmZUczlSZnovZGFHTStGYVhEclg0OTJHeXR3QktMS3dtMm5tbnAyZCtxR0ZZUDV6c1B0Y3lLVFRaNzN3ZXR3MG5pMkV5aGo0RDBDdDVIZ0szUDBndTBHT1dFZTNhVGc3NWlmTGIzTS9hRFBnNk5ndHdQTHFKS3ZzK1YzcnlWczdBZFRIcUY5clFiZktESStiZ1pMNGx4dUg5RWxINTBPMnZRUGMrVzQwTmY2TVNBUnVudVRJYkRJaHdLM0VuVEhoVGNCUHNsTS8wWUJVV3dKNno4eVhSbWkzenhCVVRGc0dLc3F3NXRwQVBRY1ZlTnJ2Nm1lUjV3WGE1bUx2ZE52UzVDRk52bGc4RTBTTlVzNVJ6VHA0eFExYXNoZER6bzZwMXB5UVZ6QkE2N1h2YkJReU9Qem5OUzdRenJLU2lxZXNlckozZEhoL08wUFJIdmxyRjBpaER0SlNMMCtUWGdMeDRvTStybExLeFZlSUJnQmdZdkp1c28iLCJtYWMiOiJlMTYyNjIzYzRkNTMwZTYwNzBhZjI0OTIzYWU1ZTc2OTZlYTEyYTAzODgxNTUzMjdmNTAxYTcxZjJlMmNmNjU1IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:37:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImRtV2RPRXoweHM2Z05UaFlsczRPL3c9PSIsInZhbHVlIjoidytIQXdEdmthZmFXTlpnbXp6REpXTkFMa1ZJRXo0anJSdDZrbXNQaWJYcjgwMHVuSmNQQjlWZFQwdGRPL0tsOTZjVW5Sa3htVWh0L1lVWUJCRlZSMks4QnZ2Vy90dm9WTTVxM1lCOCt4RHhuejBmbXFSVkRVN2JRczNKdWVrd2dpRGF5WTJqekd3Q25WRldtaER4SXNnNXdFN3pKUnhJT1B1VDFHMVN1MzVzYmZIbkNXR3oyZk9BS0tnSDlzY1UvTm1XZmYvUm1heHlibWxiMzZZa0xBaElIZlVCQnM5VWFzNmtZZzlCNDFyRVhPZjk0ajhKci9WcnZsMEJHWDlWSEE2UDU1akx5UEgyTWZDdWNINk5ZODNIQ1ZvVmx1amZoQzlhQy9kQkNOVnJPYU1zRE0wMFdpYzFIQTRvM2xNOXJFclJ1VG5paDJ2TGFSK3ZDbDhMcThIR3g4QXQyczFXTTNoazc2WHkwQWZUbzJJNTdEVzEvK3lyQjJBVkFORHlhTVBtbnliNnhQdXdXalJQTTJidkJFTlJjTDg4UGVKTk42Vm1mVmQvRjh2U0dPbkdOeTJTK01XcElPMkRkZTkrWWpJamRRMHpFSFo3MFNWS1JaKzMwR1dZUm5HV0lteFpDbDFkUFhRSHJaWnpLRzVYSGdmRzUrUHZKeUo0QzJwWU8iLCJtYWMiOiI0MmRlOTA2MTMyYTllZDdiYTQxNmJlNTRmNjM3NWM4NGU3N2YyZjA3OTE5NDQzZjE4NTFlOTAyODU1ODE5NzI3IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:37:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ik9oZTVKMld1Y1QrL2V6d2xDOFpPbnc9PSIsInZhbHVlIjoiMElUZjJTcW5aZXRncitXaUVzVE9SalZ0bHd3UHpTV0l4L2NnSU9Fc3pMWFlSaDVSK1FNbXpoMHN2a3M0d081dlFYdkFPRm9jNlRaYllGYzUvZnpPTmZxc3NmQmpqclg2RTRiMW5XM1RCUXhXVXorRjVybVBQeHQ2SHdBSEVmQnpyb3Fpa1VHMlFESmZUczlSZnovZGFHTStGYVhEclg0OTJHeXR3QktMS3dtMm5tbnAyZCtxR0ZZUDV6c1B0Y3lLVFRaNzN3ZXR3MG5pMkV5aGo0RDBDdDVIZ0szUDBndTBHT1dFZTNhVGc3NWlmTGIzTS9hRFBnNk5ndHdQTHFKS3ZzK1YzcnlWczdBZFRIcUY5clFiZktESStiZ1pMNGx4dUg5RWxINTBPMnZRUGMrVzQwTmY2TVNBUnVudVRJYkRJaHdLM0VuVEhoVGNCUHNsTS8wWUJVV3dKNno4eVhSbWkzenhCVVRGc0dLc3F3NXRwQVBRY1ZlTnJ2Nm1lUjV3WGE1bUx2ZE52UzVDRk52bGc4RTBTTlVzNVJ6VHA0eFExYXNoZER6bzZwMXB5UVZ6QkE2N1h2YkJReU9Qem5OUzdRenJLU2lxZXNlckozZEhoL08wUFJIdmxyRjBpaER0SlNMMCtUWGdMeDRvTStybExLeFZlSUJnQmdZdkp1c28iLCJtYWMiOiJlMTYyNjIzYzRkNTMwZTYwNzBhZjI0OTIzYWU1ZTc2OTZlYTEyYTAzODgxNTUzMjdmNTAxYTcxZjJlMmNmNjU1IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:37:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1378639304 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1378639304\", {\"maxDepth\":0})</script>\n"}}