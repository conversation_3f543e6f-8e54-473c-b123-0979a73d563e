{"__meta": {"id": "Xd5d3e3eb81dda5dcae7ce98976d64267", "datetime": "2025-07-29 06:34:02", "utime": **********.143348, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753770840.966227, "end": **********.143393, "duration": 1.1771659851074219, "duration_str": "1.18s", "measures": [{"label": "Booting", "start": 1753770840.966227, "relative_start": 0, "end": **********.059344, "relative_end": **********.059344, "duration": 1.0931169986724854, "duration_str": "1.09s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.059362, "relative_start": 1.***************, "end": **********.143396, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "84.03ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2999\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1805 to 1811\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1805\" onclick=\"\">routes/web.php:1805-1811</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "wvjqfQG4QwLCajUi8VqcmyVRETwrc752lI5He37H", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-1035313847 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1035313847\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-957171765 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-957171765\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1888825617 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1888825617\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1910653780 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1910653780\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1835317413 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1835317413\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-915345549 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 06:34:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkF0Q1NySVhpeGVTbVoxaDk1WmdBdHc9PSIsInZhbHVlIjoiWEJkeUFmajgvckZ0eGVkK1crTmJ6emx3aS9jU1FLRkJiMnhTZ3NYMTNuclliUjNOV1BpY1JrbmY4dUpxYkptLzZRc1NQZnVjWllsNFljQ21RL3o0UnhZcVdPSzdpZktPRDdPODNUaUJyQk12RUNYdzc1SjlLRkNEUGdGL2o2ZCtNcVhXdklKTkVRWC84OWpCVTVlY1VoY3NFd2p1SmkxeFlqNFZYZnVpbmErZXZkdXBOei9XNkJRbGViNkRrN05jd1lhR0l4UFRYTzhUajY0R3hmQndvWVRINTFtOVB6MkpRZzcvbTNUZTZXelBkc0laU3VadjR5YUYzZGhxYWhPSHhncFJJcnVmazdtR0xwSTB3enVDV2phSmtHTWdYajd3MkJvMllKQXBHb1dLMGFET3VSaytIT2cyOHJFeTZ5SGdTTE9OTlhRS1JKbzZqc2NjQTR6SVo3aVdldEgvbzZucEwzaG1yODNjelJuVWpiN3VEWW9lc1ZicFkxaUNEWW9oMkU3Rkw1T2FIN0RYR3dxYlAyek50LzhWRjZTNHFUNXo2M0ZmcTBGenJGN3h3bHV3VmNTQUUybDE4SUk3Sllkc3lrSlhwLy9FQzBXUWU2VTh4NDFXdkhxK0Z5NnAvV1lFZEMrcWFJSjhKeVRiK0ZlTXVnYUxIc2I2L0o5NXJ2a2IiLCJtYWMiOiI2ZmQzM2YzMGY5ZDdkYjE5YmM1ZmEyZWIwODFkNTRlNzI5MTljNGQ0NmIxYTc0ZDBmN2QyYjNmMmQxZmM1NGRjIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:34:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InpYVWxzd01haGdFWCtTVVp6K1VHcFE9PSIsInZhbHVlIjoielcwdXRLZkcxL1JZek9Wemt5U0p1UmEzMmlKWmlKWkhHTlJwNnRvR1czVDNUc0MyU01Qd1poWXQ3MkFTVDl6YTBNNzNORG93MVk0eU1UNXF0MWRiRjhqNmcyUVBsT3FxeW5WRjd4L2FHa1pMSUtZY3V5cjBaUFJZS0ZKWkFKNnNMWXlnNzlNQmRycUI0eFFhZkF4NEVKNGdoL2N1RTgvaUFzL1JSS1dzSkJ0Uk4xd2kvZXFtc3lBUk8ycStjNGJ2Sm9tV1pSYkJrd2Yzam5hSWozTVMrdWtlRlpUVTF3N0VETFpCem9xY3R4M25HYTJkMUMzU1l6eE9YYlRHTC9tejU4cnRzYXBpOGNtWHlwUGJMRXh6WFQzdzR4Q2lHc0tTMmtRaXpueWg4RHRDdk0zSS9lOGk3RnRmaTJkTWN0VGdRR0ZaMHVGUTJxK00wWWRkUStTL2l1cDhVd2w1YUhoQkFGYmhZL0JETXF1c2RIbWE4VVR5NkdGUzBHN2VGWDNGNXRKNUpFZVR2RjlJenBONkhoSVR6b3F6Q29wemtTc3BDb2IxY3dPeU8xd01EdE55QzlSNWNwbVArRGw4d3p1WDdlMG8yeFhmMWdBbjJKZDJHbk4xTXMwZyttQUgzWTg5MFp5ZENSM2d3OC9GbjlmZkE3V1kwbDg4cm5lTDBLOG8iLCJtYWMiOiIwMjg3ZjQ1NTI2NWI5MjI3OWM5MDk4MWZlZjIxNDVhN2ZiODEwNmM0YTQ3OGIwMjcxMzM4OTBiYWFkZTdhYWY5IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 08:34:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkF0Q1NySVhpeGVTbVoxaDk1WmdBdHc9PSIsInZhbHVlIjoiWEJkeUFmajgvckZ0eGVkK1crTmJ6emx3aS9jU1FLRkJiMnhTZ3NYMTNuclliUjNOV1BpY1JrbmY4dUpxYkptLzZRc1NQZnVjWllsNFljQ21RL3o0UnhZcVdPSzdpZktPRDdPODNUaUJyQk12RUNYdzc1SjlLRkNEUGdGL2o2ZCtNcVhXdklKTkVRWC84OWpCVTVlY1VoY3NFd2p1SmkxeFlqNFZYZnVpbmErZXZkdXBOei9XNkJRbGViNkRrN05jd1lhR0l4UFRYTzhUajY0R3hmQndvWVRINTFtOVB6MkpRZzcvbTNUZTZXelBkc0laU3VadjR5YUYzZGhxYWhPSHhncFJJcnVmazdtR0xwSTB3enVDV2phSmtHTWdYajd3MkJvMllKQXBHb1dLMGFET3VSaytIT2cyOHJFeTZ5SGdTTE9OTlhRS1JKbzZqc2NjQTR6SVo3aVdldEgvbzZucEwzaG1yODNjelJuVWpiN3VEWW9lc1ZicFkxaUNEWW9oMkU3Rkw1T2FIN0RYR3dxYlAyek50LzhWRjZTNHFUNXo2M0ZmcTBGenJGN3h3bHV3VmNTQUUybDE4SUk3Sllkc3lrSlhwLy9FQzBXUWU2VTh4NDFXdkhxK0Z5NnAvV1lFZEMrcWFJSjhKeVRiK0ZlTXVnYUxIc2I2L0o5NXJ2a2IiLCJtYWMiOiI2ZmQzM2YzMGY5ZDdkYjE5YmM1ZmEyZWIwODFkNTRlNzI5MTljNGQ0NmIxYTc0ZDBmN2QyYjNmMmQxZmM1NGRjIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:34:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InpYVWxzd01haGdFWCtTVVp6K1VHcFE9PSIsInZhbHVlIjoielcwdXRLZkcxL1JZek9Wemt5U0p1UmEzMmlKWmlKWkhHTlJwNnRvR1czVDNUc0MyU01Qd1poWXQ3MkFTVDl6YTBNNzNORG93MVk0eU1UNXF0MWRiRjhqNmcyUVBsT3FxeW5WRjd4L2FHa1pMSUtZY3V5cjBaUFJZS0ZKWkFKNnNMWXlnNzlNQmRycUI0eFFhZkF4NEVKNGdoL2N1RTgvaUFzL1JSS1dzSkJ0Uk4xd2kvZXFtc3lBUk8ycStjNGJ2Sm9tV1pSYkJrd2Yzam5hSWozTVMrdWtlRlpUVTF3N0VETFpCem9xY3R4M25HYTJkMUMzU1l6eE9YYlRHTC9tejU4cnRzYXBpOGNtWHlwUGJMRXh6WFQzdzR4Q2lHc0tTMmtRaXpueWg4RHRDdk0zSS9lOGk3RnRmaTJkTWN0VGdRR0ZaMHVGUTJxK00wWWRkUStTL2l1cDhVd2w1YUhoQkFGYmhZL0JETXF1c2RIbWE4VVR5NkdGUzBHN2VGWDNGNXRKNUpFZVR2RjlJenBONkhoSVR6b3F6Q29wemtTc3BDb2IxY3dPeU8xd01EdE55QzlSNWNwbVArRGw4d3p1WDdlMG8yeFhmMWdBbjJKZDJHbk4xTXMwZyttQUgzWTg5MFp5ZENSM2d3OC9GbjlmZkE3V1kwbDg4cm5lTDBLOG8iLCJtYWMiOiIwMjg3ZjQ1NTI2NWI5MjI3OWM5MDk4MWZlZjIxNDVhN2ZiODEwNmM0YTQ3OGIwMjcxMzM4OTBiYWFkZTdhYWY5IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 08:34:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-915345549\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-79037013 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wvjqfQG4QwLCajUi8VqcmyVRETwrc752lI5He37H</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-79037013\", {\"maxDepth\":0})</script>\n"}}